"use client"

import type React from "react"

import { use<PERSON><PERSON><PERSON>, EditorContent } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Placeholder from "@tiptap/extension-placeholder"
import Link from "@tiptap/extension-link"
import Image from "@tiptap/extension-image"
import { useState, useEffect, useCallback, useRef } from "react"
import { Button } from "@/components/ui/button"
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  LinkIcon,
  ImageIcon,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Minus,
  AtSign,
  Search,
  X,
  BookOpen,
  Wrench,
  AlertTriangle,
  FlaskConical,
  LightbulbIcon,
  HelpCircle,
  MessageSquare,
  Zap,
  FileText,
  MessageCircle,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Di<PERSON>, DialogContent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { CardExtension } from "./tiptap-card-extension"

// 定义内容类型
type ContentType = "viewpoint" | "discussion"

interface TiptapEditorProps {
  content: string
  onChange: (content: string) => void
  placeholder?: string
  className?: string
  error?: boolean
  minHeight?: string
}

export function TiptapEditor({
  content,
  onChange,
  placeholder = "開始輸入內容...",
  className,
  error = false,
  minHeight = "200px",
}: TiptapEditorProps) {
  const [linkUrl, setLinkUrl] = useState("")
  const [imageUrl, setImageUrl] = useState("")
  const [isLinkPopoverOpen, setIsLinkPopoverOpen] = useState(false)
  const [isImagePopoverOpen, setIsImagePopoverOpen] = useState(false)
  const [isCardSelectorOpen, setIsCardSelectorOpen] = useState(false)
  const editorRef = useRef<HTMLDivElement>(null)

  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline underline-offset-2",
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: "rounded-md max-w-full my-4",
        },
      }),
      CardExtension,
    ],
    content: content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: "prose prose-sm sm:prose-base dark:prose-invert focus:outline-none max-w-none prose-compact",
        style: `min-height: ${minHeight}; padding: 0.5rem;`,
      },
    },
    immediatelyRender: false,
  })

  // Sync content from props to editor
  useEffect(() => {
    if (editor && content === "") {
      editor.commands.setContent("")
    }
  }, [content, editor])

  const addLink = useCallback(() => {
    if (!editor) return

    if (linkUrl) {
      editor.chain().focus().extendMarkRange("link").setLink({ href: linkUrl }).run()

      setLinkUrl("")
      setIsLinkPopoverOpen(false)
    }
  }, [editor, linkUrl])

  const addImage = useCallback(() => {
    if (!editor) return

    if (imageUrl) {
      editor.chain().focus().setImage({ src: imageUrl }).run()

      setImageUrl("")
      setIsImagePopoverOpen(false)
    }
  }, [editor, imageUrl])

  // 處理卡片選擇
  const handleSelectCard = useCallback(
    (card: any) => {
      if (!editor) return

      // 使用自定義節點插入卡片引用
      editor
        .chain()
        .focus()
        .insertContent({
          type: "cardQuote",
          attrs: {
            cardId: card.id,
            cardTitle: card.title,
            cardContent: card.content,
            cardAuthor: card.author,
            cardType: card.type,
            cardTags: card.tags,
            isLeader: card.isLeader,
            contentType: card.contentType, // 添加内容类型
          },
        })
        .run()

      // 關閉卡片選擇器
      setIsCardSelectorOpen(false)
    },
    [editor],
  )

  // 打開卡片選擇器
  const handleOpenCardSelector = useCallback(() => {
    setIsCardSelectorOpen(true)
  }, [])

  if (!editor) {
    return null
  }

  return (
    <div
      className={cn(
        "border rounded-md overflow-hidden",
        error ? "border-destructive" : "border-border/40 focus-within:ring-1 focus-within:ring-ring",
        className,
      )}
      ref={editorRef}
    >
      <div className="flex flex-wrap gap-1 p-1 border-b bg-muted/40">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={cn("h-8 w-8", editor.isActive("bold") ? "bg-muted" : "")}
          type="button"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={cn("h-8 w-8", editor.isActive("italic") ? "bg-muted" : "")}
          type="button"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
          className={cn("h-8 w-8", editor.isActive("heading", { level: 1 }) ? "bg-muted" : "")}
          type="button"
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={cn("h-8 w-8", editor.isActive("heading", { level: 2 }) ? "bg-muted" : "")}
          type="button"
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={cn("h-8 w-8", editor.isActive("heading", { level: 3 }) ? "bg-muted" : "")}
          type="button"
        >
          <Heading3 className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={cn("h-8 w-8", editor.isActive("bulletList") ? "bg-muted" : "")}
          type="button"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={cn("h-8 w-8", editor.isActive("orderedList") ? "bg-muted" : "")}
          type="button"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={cn("h-8 w-8", editor.isActive("blockquote") ? "bg-muted" : "")}
          type="button"
        >
          <Quote className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={cn("h-8 w-8", editor.isActive("codeBlock") ? "bg-muted" : "")}
          type="button"
        >
          <Code className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().setHorizontalRule().run()}
          className="h-8 w-8"
          type="button"
        >
          <Minus className="h-4 w-4" />
        </Button>

        <Popover open={isLinkPopoverOpen} onOpenChange={setIsLinkPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className={cn("h-8 w-8", editor.isActive("link") ? "bg-muted" : "")}
              type="button"
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-3">
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium">添加連結</div>
              <div className="flex gap-2">
                <Input
                  placeholder="https://example.com"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={addLink} type="button" size="sm">
                  添加
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Popover open={isImagePopoverOpen} onOpenChange={setIsImagePopoverOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8" type="button">
              <ImageIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-3">
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium">添加圖片</div>
              <div className="flex gap-2">
                <Input
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={addImage} type="button" size="sm">
                  添加
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 添加卡片引用按鈕（帶提示） */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8" type="button" onClick={handleOpenCardSelector}>
                <AtSign className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>點選 @ 可以引用卡片或討論串</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="ml-auto flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="h-8 w-8"
            type="button"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="h-8 w-8"
            type="button"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <EditorContent editor={editor} className="overflow-y-auto" />

      {/* 卡片選擇器對話框 */}
      <Dialog open={isCardSelectorOpen} onOpenChange={setIsCardSelectorOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>選擇要引用的內容</DialogTitle>
          </DialogHeader>
          <CardList onSelectCard={handleSelectCard} />
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 内容类型配置
const contentTypeConfig: Record<
  ContentType,
  {
    icon: React.ReactNode
    label: string
    color: string
  }
> = {
  viewpoint: {
    icon: <FileText className="h-3.5 w-3.5" />,
    label: "觀點卡",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  },
  discussion: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論串",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  },
}

// 卡片列表組件
function CardList({ onSelectCard }: { onSelectCard: (card: any) => void }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredCards, setFilteredCards] = useState(mockCards)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [hoveredIndex, setHoveredIndex] = useState(-1)
  const [activeFilter, setActiveFilter] = useState<ContentType | "all">("all") // 添加过滤状态

  // 當搜索詞變化時過濾卡片
  useEffect(() => {
    // 首先按内容类型过滤
    let filtered = mockCards
    if (activeFilter !== "all") {
      filtered = mockCards.filter((card) => card.contentType === activeFilter)
    }

    // 检查特殊前缀
    if (searchTerm === "card" || searchTerm.startsWith("card:")) {
      const specificTerm = searchTerm.startsWith("card:") ? searchTerm.substring(5).trim() : ""
      filtered = mockCards.filter(
        (card) =>
          card.contentType === "viewpoint" &&
          (specificTerm === "" ||
            card.title.toLowerCase().includes(specificTerm) ||
            card.content.toLowerCase().includes(specificTerm) ||
            card.tags.some((tag: string) => tag.toLowerCase().includes(specificTerm)) ||
            card.author.toLowerCase().includes(specificTerm) ||
            card.type.toLowerCase().includes(specificTerm)),
      )
    } else if (searchTerm === "thread" || searchTerm.startsWith("thread:")) {
      const specificTerm = searchTerm.startsWith("thread:") ? searchTerm.substring(7).trim() : ""
      filtered = mockCards.filter(
        (card) =>
          card.contentType === "discussion" &&
          (specificTerm === "" ||
            card.title.toLowerCase().includes(specificTerm) ||
            card.content.toLowerCase().includes(specificTerm) ||
            card.tags.some((tag: string) => tag.toLowerCase().includes(specificTerm)) ||
            card.author.toLowerCase().includes(specificTerm) ||
            card.type.toLowerCase().includes(specificTerm)),
      )
    } else if (searchTerm.trim()) {
      // 常规搜索
      const lowerSearchTerm = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (card) =>
          card.title.toLowerCase().includes(lowerSearchTerm) ||
          card.content.toLowerCase().includes(lowerSearchTerm) ||
          card.tags.some((tag: string) => tag.toLowerCase().includes(lowerSearchTerm)) ||
          card.author.toLowerCase().includes(lowerSearchTerm) ||
          card.type.toLowerCase().includes(lowerSearchTerm),
      )
    }

    setFilteredCards(filtered)
  }, [searchTerm, activeFilter])

  // 按内容类型分组
  const viewpointCards = filteredCards.filter((card) => card.contentType === "viewpoint")
  const discussionCards = filteredCards.filter((card) => card.contentType === "discussion")

  // 处理过滤器点击
  const handleFilterClick = (filter: ContentType | "all") => {
    setActiveFilter(filter)
    setSelectedIndex(-1)
    setHoveredIndex(-1)
  }

  // 截斷內容
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  // 渲染卡片项
  const renderCardItem = (card: any, index: number, globalIndex: number) => {
    const typeConfig = semanticTypeConfig[card.type as string] || semanticTypeConfig.concept
    const sourceConfig = sourceTypeConfig[card.isLeader ? "leader" : "community"]
    const contentConfig = contentTypeConfig[card.contentType]

    return (
      <div
        key={card.id}
        className={cn(
          "px-3 py-2 cursor-pointer hover:bg-muted border-b border-border/40 last:border-b-0",
          (globalIndex === selectedIndex || globalIndex === hoveredIndex) && "bg-muted",
        )}
        onClick={() => onSelectCard(card)}
        onMouseEnter={() => setHoveredIndex(globalIndex)}
        onMouseLeave={() => setHoveredIndex(-1)}
      >
        <div className="flex flex-col gap-1">
          {/* 卡片头部 - 类型徽章和标签 */}
          <div className="flex flex-wrap items-center gap-2">
            {/* 内容类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", contentConfig.color)}>
              {contentConfig.icon}
              <span>{contentConfig.label}</span>
            </Badge>

            {/* 语义类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主题徽章 */}
            {card.tags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{card.tags[0]}</span>
              </Badge>
            )}

            {/* 来源类型徽章 */}
            {card.isLeader && sourceConfig.show && (
              <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                <span>{sourceConfig.badge}</span>
              </Badge>
            )}

            {/* 热门标签 */}
            {card.isHot && (
              <Badge variant="destructive" className="text-xs py-0">
                熱門
              </Badge>
            )}
          </div>

          {/* 作者信息 */}
          <div className="text-xs text-muted-foreground">作者：{card.author}</div>

          {/* 标题 */}
          <h4 className="text-sm font-bold text-foreground">{card.title}</h4>

          {/* 内容 */}
          <div className="text-xs text-muted-foreground">{truncateText(card.content, 80)}</div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 mt-1">
            {card.tags.map((tag: string, tagIndex: number) => (
              <Badge key={tagIndex} variant="outline" className="text-xs py-0">
                #{tag}
              </Badge>
            ))}
          </div>

          {/* 讨论串特有信息 */}
          {card.contentType === "discussion" && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3.5 w-3.5" />
                <span>{card.replies || 0}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* 过滤器选项 */}
      <div className="flex items-center p-2 gap-1 border-b flex-nowrap">
        <Badge
          variant={activeFilter === "all" ? "default" : "outline"}
          className="cursor-pointer flex items-center gap-1 whitespace-nowrap"
          onClick={() => handleFilterClick("all")}
        >
          全部
        </Badge>
        <Badge
          variant={activeFilter === "viewpoint" ? "default" : "outline"}
          className={cn(
            "cursor-pointer flex items-center gap-1 whitespace-nowrap",
            activeFilter === "viewpoint" ? "bg-blue-600" : "",
          )}
          onClick={() => handleFilterClick("viewpoint")}
        >
          {contentTypeConfig.viewpoint.icon}
          觀點卡
        </Badge>
        <Badge
          variant={activeFilter === "discussion" ? "default" : "outline"}
          className={cn(
            "cursor-pointer flex items-center gap-1 whitespace-nowrap",
            activeFilter === "discussion" ? "bg-purple-600" : "",
          )}
          onClick={() => handleFilterClick("discussion")}
        >
          {contentTypeConfig.discussion.icon}
          討論串
        </Badge>

      </div>

      <div className="relative mb-4">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索標題、內容、標籤或作者..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          autoFocus
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1 h-7 w-7 p-0"
            onClick={() => setSearchTerm("")}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">清除搜索</span>
          </Button>
        )}
      </div>
      <ScrollArea className="h-[400px] pr-4">
        <div className="space-y-1">
          {filteredCards.length > 0 ? (
            <>
              {/* 如果有观点卡且不是只显示讨论串 */}
              {viewpointCards.length > 0 && activeFilter !== "discussion" && (
                <>
                  {/* 只有当同时显示两种类型时才显示分组标题 */}
                  {activeFilter === "all" && <div className="px-3 py-1 bg-muted/50 font-medium text-sm">觀點卡</div>}
                  {viewpointCards.map((card, index) => renderCardItem(card, index, index))}
                </>
              )}

              {/* 如果有讨论串且不是只显示观点卡 */}
              {discussionCards.length > 0 && activeFilter !== "viewpoint" && (
                <>
                  {/* 只有当同时显示两种类型且有观点卡时才添加分隔 */}
                  {activeFilter === "all" && viewpointCards.length > 0 && (
                    <div className="px-3 py-1 bg-muted/50 font-medium text-sm">討論串</div>
                  )}
                  {discussionCards.map((card, index) => renderCardItem(card, index, viewpointCards.length + index))}
                </>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>沒有找到符合條件的內容</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </>
  )
}

// Helper function to convert HTML to plain text (for character count)
export function htmlToPlainText(html: string): string {
  const doc = new DOMParser().parseFromString(html, "text/html")
  return doc.body.textContent || ""
}

// 模擬卡片數據 - 實際應用中應從數據庫獲取
const mockCards = [
  {
    id: 101,
    type: "concept",
    title: "RAG 系統基本概念",
    content: "RAG（Retrieval-Augmented Generation）是一種結合檢索和生成的方法，用於增強大型語言模型的回答能力...",
    tags: ["RAG", "LLM", "AI"],
    author: "AI研究員",
    isLeader: true,
    contentType: "viewpoint" as ContentType,
  },
  {
    id: 102,
    type: "implementation",
    title: "如何實現高效的向量檢索",
    content: "向量檢索是 RAG 系統的核心組件，本文介紹幾種常用的向量檢索方法及其優缺點...",
    tags: ["向量檢索", "FAISS", "Pinecone"],
    author: "系統架構師",
    isLeader: false,
    contentType: "viewpoint" as ContentType,
  },
  {
    id: 103,
    type: "warning",
    title: "RAG 系統常見陷阱",
    content: "構建 RAG 系統時，有幾個常見的陷阱需要避免：1. 檢索結果不相關；2. 生成內容與檢索內容不一致...",
    tags: ["RAG", "陷阱", "最佳實踐"],
    author: "資深工程師",
    isLeader: false,
    contentType: "viewpoint" as ContentType,
  },
  {
    id: 104,
    type: "insight",
    title: "RAG vs 微調：何時選擇哪種方法",
    content: "RAG 和模型微調都是增強 LLM 能力的方法，但它們適用於不同的場景。本文分析兩種方法的優缺點和適用場景...",
    tags: ["RAG", "微調", "LLM"],
    author: "AI策略師",
    isLeader: true,
    contentType: "viewpoint" as ContentType,
  },
  {
    id: 105,
    type: "experience",
    title: "RAG 系統評估指標全解析",
    content: "如何評估 RAG 系統的性能？本文介紹了一系列評估指標，包括檢索準確率、生成質量、事實一致性等...",
    tags: ["RAG", "評估", "指標"],
    author: "評估專家",
    isLeader: false,
    contentType: "viewpoint" as ContentType,
  },
  {
    id: 106,
    type: "debate",
    title: "提高 RAG 系統檢索質量的 5 個技巧",
    content: "想要提高 RAG 系統的檢索質量？這裡有 5 個實用技巧：1. 優化文檔切分；2. 使用混合檢索；3. 查詢重寫...",
    tags: ["RAG", "檢索質量", "優化"],
    author: "優化專家",
    isLeader: false,
    contentType: "viewpoint" as ContentType,
  },
  // 添加讨论串数据
  {
    id: 201,
    type: "discussion",
    title: "大家都在用什麼向量數據庫？",
    content: "我正在選擇向量數據庫，目前考慮 Pinecone、Milvus 和 Weaviate，大家有什麼建議嗎？",
    tags: ["向量數據庫", "RAG", "技術選型"],
    author: "新手工程師",
    replies: 12,
    views: 156,
    contentType: "discussion" as ContentType,
  },
  {
    id: 202,
    type: "question",
    title: "如何解決 RAG 系統的幻覺問題？",
    content: "我的 RAG 系統經常產生與檢索內容不符的回答，有什麼好的解決方案嗎？",
    tags: ["RAG", "幻覺", "LLM"],
    author: "困惑的開發者",
    replies: 8,
    views: 103,
    contentType: "discussion" as ContentType,
  },
  {
    id: 203,
    type: "brainstorm",
    title: "集思廣益：RAG 系統的創新應用",
    content: "除了問答和摘要，RAG 還有哪些有趣的應用場景？讓我們一起頭腦風暴！",
    tags: ["RAG", "創新", "應用場景"],
    author: "創意總監",
    replies: 24,
    views: 287,
    isHot: true,
    contentType: "discussion" as ContentType,
  },
  {
    id: 204,
    type: "chat",
    title: "分享你的 RAG 系統架構",
    content: "我剛完成了一個 RAG 系統的部署，使用了 LangChain + Pinecone + GPT-4，大家來分享一下各自的架構吧！",
    tags: ["RAG", "架構", "經驗分享"],
    author: "系統工程師",
    replies: 15,
    views: 198,
    contentType: "discussion" as ContentType,
  },
]

// 語義類型配置
const semanticTypeConfig: Record<
  string,
  {
    icon: React.ReactNode
    label: string
    description: string
    color: string
  }
> = {
  concept: {
    icon: <BookOpen className="h-3.5 w-3.5" />,
    label: "概念整理",
    description: "原理、理論、詞彙解釋、系統性知識輸出",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
  implementation: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "實作",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  warning: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  experience: {
    icon: <FlaskConical className="h-3.5 w-3.5" />,
    label: "實測經驗",
    description: "自己做過的實驗結果、性能比較、效果說明",
    color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
  },
  insight: {
    icon: <LightbulbIcon className="h-3.5 w-3.5" />,
    label: "看法",
    description: "對該主題的觀點、價值判斷、立場",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  debate: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "爭議論點",
    description: "值不值得做？哪個方法比較爛？",
    color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
  },
  guide: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "工具教學",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  trap: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  // 添加讨论类型配置
  discussion: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論",
    description: "一般討論主題",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  question: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "問題",
    description: "尋求解答的問題",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  brainstorm: {
    icon: <Zap className="h-3.5 w-3.5" />,
    label: "集思廣益",
    description: "集體思考和創意討論",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  chat: {
    icon: <MessageCircle className="h-3.5 w-3.5" />,
    label: "閒聊",
    description: "輕鬆的交流討論",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
}

// 卡片來源類型配置
const sourceTypeConfig: Record<
  string,
  {
    label: string
    badge: string
    color: string
    show: boolean
  }
> = {
  top_author: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
  community: {
    label: "社群貢獻",
    badge: "Community",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    show: true,
  },
  curated: {
    label: "系統整理",
    badge: "Curated",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    show: true,
  },
  original: {
    label: "原創內容",
    badge: "原創內容",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    show: true,
  },
  others: {
    label: "他人觀點",
    badge: "",
    color: "",
    show: false,
  },
  leader: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
}
