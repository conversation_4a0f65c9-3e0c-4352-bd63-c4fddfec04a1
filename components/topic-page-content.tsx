"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BookMarked, MessageSquare, Plus, Star, FileText } from "lucide-react"
import Link from "next/link"
import { useState, useEffect, useRef } from "react"

// 在組件頂部添加 useSearchParams 和 useEffect
import { useSearchParams, useRouter } from "next/navigation"
import { TopicDataLoader } from "@/components/topic-data-loader"

interface TopicPageContentProps {
  topic: {
    id: string
    name: string
    description: string
    slug: string
    subtopics?: {
      id: string
      name: string
      description: string
      slug: string
    }[]
  }
  initialSubtopic?: string
}

export function TopicPageContent({ topic, initialSubtopic }: TopicPageContentProps) {
  const [activeSubtopic, setActiveSubtopic] = useState<string | null>(initialSubtopic || null)
  const [activeTab, setActiveTab] = useState<string>("viewpoints")
  const initialRenderRef = useRef(true)

  // 在 TopicPageContent 組件內部，添加以下代碼來處理 URL 查詢參數
  const searchParams = useSearchParams()
  const router = useRouter()
  const subtopicParam = searchParams.get("subtopic")

  // 使用 useEffect 來設置 activeSubtopic，但只在初始渲染時執行一次
  useEffect(() => {
    if (!initialRenderRef.current) return

    if (initialSubtopic) {
      // If an initial subtopic is provided, use it directly
      setActiveSubtopic(initialSubtopic)
      initialRenderRef.current = false
    } else if (subtopicParam) {
      // Otherwise check URL query parameters
      const normalizedSubtopic = subtopicParam.toLowerCase()
      const matchedSubtopic = topic.subtopics?.find((sub) => sub.slug.toLowerCase() === normalizedSubtopic)

      if (matchedSubtopic) {
        setActiveSubtopic(matchedSubtopic.name)
      } else {
        // If not in predefined subtopics, use it directly
        setActiveSubtopic(subtopicParam)
      }
      initialRenderRef.current = false
    } else {
      initialRenderRef.current = false
    }
  }, [initialSubtopic, subtopicParam, topic.subtopics])

  // 修改 setActiveSubtopic 的點擊處理函數，使其更新 URL
  const handleSubtopicClick = (subtopic: string | null) => {
    if (subtopic === activeSubtopic) {
      // 如果點擊當前活動的子主題，則清除選擇
      setActiveSubtopic(null)
      router.push(`/topic/${topic.slug}`, undefined, { scroll: false })
    } else {
      // 否則設置新的子主題
      setActiveSubtopic(subtopic)
      if (subtopic) {
        // Find the subtopic slug
        const subtopicObj = topic.subtopics?.find((sub) => sub.name === subtopic)
        const subtopicSlug = subtopicObj?.slug || subtopic.toLowerCase()

        // 使用 encodeURIComponent 確保中文字符在 URL 中被正確編碼
        router.push(`/topic/${topic.slug}/${encodeURIComponent(subtopicSlug)}`, undefined, { scroll: false })
      } else {
        router.push(`/topic/${topic.slug}`, undefined, { scroll: false })
      }
    }
  }

  // 模擬數據 - 觀點卡
  const topicCards = [
    {
      id: 101,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user1",
        name: "User1",
        avatar: "/abstract-geometric-shapes.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-15T08:30:00Z",
      timestamp: "2023-05-15T08:30:00Z",
      stats: {
        likes: 24,
        dislikes: 3,
        comments: 8,
        bookmarks: 12,
      },
      features: {
        truncate: true,
      },
    },
    {
      id: 102,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user2",
        name: "User2",
        avatar: "/Dublin-Skyline-Concert.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-16T10:45:00Z",
      timestamp: "2023-05-16T10:45:00Z",
      stats: {
        likes: 18,
        dislikes: 2,
        comments: 5,
        bookmarks: 9,
      },
      features: {
        truncate: true,
      },
    },
    {
      id: 103,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user3",
        name: "User3",
        avatar: "/abstract-geometric-shapes.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-17T14:20:00Z",
      timestamp: "2023-05-17T14:20:00Z",
      stats: {
        likes: 32,
        dislikes: 4,
        comments: 12,
        bookmarks: 15,
      },
      features: {
        truncate: true,
      },
    },
    {
      id: 104,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user1",
        name: "User1",
        avatar: "/abstract-geometric-shapes.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-18T09:15:00Z",
      timestamp: "2023-05-18T09:15:00Z",
      stats: {
        likes: 27,
        dislikes: 1,
        comments: 9,
        bookmarks: 14,
      },
      features: {
        truncate: true,
      },
    },
    {
      id: 105,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user2",
        name: "User2",
        avatar: "/Dublin-Skyline-Concert.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-19T16:40:00Z",
      timestamp: "2023-05-19T16:40:00Z",
      stats: {
        likes: 21,
        dislikes: 2,
        comments: 7,
        bookmarks: 11,
      },
      features: {
        truncate: true,
      },
    },
    {
      id: 106,
      contentType: "viewpoint" as const,
      semanticType: "implementation" as const,
      variant: "default" as const,
      title: `實作案例: ${activeSubtopic || ""} 在企業應用中的落地經驗`,
      content: `在將 ${activeSubtopic || topic.name} 技術應用於企業場景時，我們發現幾個關鍵成功因素。首先，明確的問題定義和評估指標至關重要；其次，技術選型需考慮實際業務場景和資源限制；最後，持續優化和反饋循環是保持系統效能的關鍵。\n\n實踐要點：\n- 從小規模試點開始，逐步擴大應用範圍\n- 建立明確的評估指標和基準測試\n- 結合業務專家和技術團隊的緊密協作`,
      topics: [topic.name],
      subtopics: [activeSubtopic || topic.subtopics[1], "企業應用"],
      author: {
        id: "user3",
        name: "User3",
        avatar: "/abstract-geometric-shapes.png",
      },
      sourceType: "original" as const,
      createdAt: "2023-05-20T11:30:00Z",
      timestamp: "2023-05-20T11:30:00Z",
      stats: {
        likes: 29,
        dislikes: 3,
        comments: 10,
        bookmarks: 13,
      },
      features: {
        truncate: true,
      },
    },
  ]

  // 格式化相對時間的函數
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return `${diffInSeconds}秒前`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分鐘前`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小時前`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}個月前`
    return `${Math.floor(diffInSeconds / 31536000)}年前`
  }

  // 模擬數據 - 討論串
  const discussionThreads = [
    {
      id: 201,
      contentType: "discussion" as const,
      variant: "default" as const,
      title: `Welcome to meta.discourse.org`,
      author: "community",
      replies: 0,
      views: 77500,
      lastActive: "Feb '23",
    },
    {
      id: 202,
      contentType: "discussion" as const,
      variant: "default" as const,
      title: `Any Discourse use case for the fashion market?`,
      author: "community",
      replies: 1,
      views: 24,
      lastActive: "5m",
    },
    {
      id: 203,
      contentType: "discussion" as const,
      variant: "default" as const,
      title: `Discourse footnote`,
      author: "plugin",
      replies: 1,
      views: 9700,
      lastActive: "29m",
    },
    {
      id: 204,
      contentType: "discussion" as const,
      variant: "default" as const,
      title: `Excluding certain categories from the trust levels`,
      author: "feature",
      replies: 21,
      views: 200,
      lastActive: "1h",
    },
    {
      id: 205,
      contentType: "discussion" as const,
      variant: "default" as const,
      title: `Webhook: Include X-Discourse-Event in transmitted info?`,
      author: "plugin",
      replies: 0,
      views: 21,
      lastActive: "2h",
    },
  ]

  // 過濾卡片基於選中的子主題
  const filteredCards = activeSubtopic
    ? topicCards.filter((card) => card.subtopics.includes(activeSubtopic))
    : topicCards

  // 模擬統計數據
  const stats = {
    cards: 42,
    discussions: 128,
    bookmarks: 76,
  }

  const [isSticky, setIsSticky] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const grayTitleArea = document.querySelector(".bg-gray-50, .dark\\:bg-gray-900")
      const threshold = grayTitleArea ? grayTitleArea.getBoundingClientRect().bottom + window.scrollY : 300

      setIsSticky(scrollPosition > threshold)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <div className="max-w-[1400px] mx-auto px-4 space-y-6">
      {/* 麵包屑導航 */}
      <nav className="flex items-center text-sm text-muted-foreground mb-4">
        <Link href="/" className="hover:text-foreground flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          首頁
        </Link>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 mx-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <Link href={`/topic/${topic.slug}`} className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">
          {topic.name}
        </Link>
        {activeSubtopic && (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mx-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <Link
              href={`/topic/${topic.slug}/${encodeURIComponent(
                topic.subtopics?.find((sub) => sub.name === activeSubtopic)?.slug || activeSubtopic.toLowerCase(),
              )}`}
              className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
            >
              #{activeSubtopic}
            </Link>
          </>
        )}
      </nav>

      {/* 主題標題、描述和子主題篩選列 */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        {/* 主題標題和描述 */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold flex items-center">
              {activeSubtopic ? <span>#{activeSubtopic}</span> : <span>{topic.name}</span>}
            </h1>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="flex items-center text-rose-500">
                  <BookMarked className="h-5 w-5 mr-1" />
                  <span className="font-medium">{stats.cards}</span>
                </div>
                <div className="flex items-center text-blue-500">
                  <MessageSquare className="h-5 w-5 mr-1" />
                  <span className="font-medium">{stats.discussions}</span>
                </div>
                <div className="flex items-center text-amber-500">
                  <Star className="h-5 w-5 mr-1" />
                  <span className="font-medium">{stats.bookmarks}</span>
                </div>
              </div>
              <Button size="sm" className="gap-2">
                <Plus className="h-4 w-4" />
                關注此標籤
              </Button>
            </div>
          </div>
          <p className="text-muted-foreground">
            {activeSubtopic ? `智能體之間的通訊和協作機制，是多智能體系統的核心技術` : topic.description}
          </p>
        </div>

        {/* 子主題篩選列 */}
        <div className="flex flex-wrap items-center gap-3">
          <span className="text-sm font-medium text-muted-foreground">
            {activeSubtopic ? "切換子主題:" : "可選子主題:"}
          </span>
          <div className="flex flex-wrap gap-2">
            <Badge
              variant={!activeSubtopic ? "default" : "outline"}
              className="cursor-pointer py-1.5 px-3 text-sm"
              onClick={() => handleSubtopicClick(null)}
            >
              全部
            </Badge>
            {topic.subtopics?.map((subtopic) => (
              <Badge
                key={subtopic.id}
                variant={activeSubtopic === subtopic.name ? "default" : "outline"}
                className="cursor-pointer py-1.5 px-3 text-sm"
                onClick={() => handleSubtopicClick(subtopic.name === activeSubtopic ? null : subtopic.name)}
              >
                {subtopic.name}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 主要內容區 - 標籤頁 */}
      <div className="pt-4 pb-0">
        {/* Tabs with proper structure */}
        <Tabs defaultValue="viewpoints" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div
            className={`sticky-header ${isSticky ? "sticky" : ""}`}
            style={{
              position: "sticky",
              top: 0,
              zIndex: 40,
              backgroundColor: isSticky ? "rgba(255, 255, 255, 0.95)" : "transparent",
              backdropFilter: isSticky ? "blur(8px)" : "none",
              padding: isSticky ? "0.75rem 0" : "0",
              transform: isSticky ? "translateY(0)" : "translateY(-100%)",
              transition: "transform 0.3s ease-in-out, background-color 0.3s ease",
              borderBottom: isSticky ? "1px solid rgba(0,0,0,0.05)" : "none",
              marginTop: 0,
            }}
          >
            {/* Breadcrumb navigation - only visible when sticky */}
            {isSticky && (
              <nav className="flex items-center text-sm text-muted-foreground mb-2">
                <Link href="/" className="hover:text-foreground flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  首頁
                </Link>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mx-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
                <Link href={`/topic/${topic.slug}`} className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">
                  {topic.name}
                </Link>
                {activeSubtopic && (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mx-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                    <Link
                      href={`/topic/${topic.slug}/${encodeURIComponent(
                        topic.subtopics?.find((sub) => sub.name === activeSubtopic)?.slug ||
                          activeSubtopic.toLowerCase(),
                      )}`}
                      className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
                    >
                      #{activeSubtopic}
                    </Link>
                  </>
                )}
              </nav>
            )}

            {/* Tabs and search bar */}
            <div
              className="flex justify-between items-center border-b pb-1"
              style={{
                marginTop: isSticky ? 0 : "1.5rem",
                marginBottom: isSticky ? 0 : "0.25rem",
                paddingTop: isSticky ? "0.5rem" : "0",
                backgroundColor: "inherit", // Inherit from parent to maintain transparency
                position: "relative", // Ensure proper stacking
                zIndex: 1, // Lower than parent but still above content
              }}
            >
              <TabsList className="bg-transparent h-auto p-0">
                <TabsTrigger
                  value="viewpoints"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-2 bg-transparent"
                >
                  觀點卡
                </TabsTrigger>
                <TabsTrigger
                  value="discussions"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-2 bg-transparent"
                >
                  討論區
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <div className="relative w-64">
                  <Input placeholder={activeTab === "viewpoints" ? "搜尋觀點..." : "搜尋討論..."} className="pl-8" />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <Button variant="outline" size="icon" className="h-10 w-10">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                    />
                  </svg>
                </Button>
                <Button className="gap-2">
                  <FileText className="h-4 w-4" />
                  投稿觀點
                </Button>
              </div>
            </div>
          </div>

          <TabsContent value="viewpoints" className="mt-0 space-y-4">
            <TopicDataLoader
              topicId={topic.id}
              activeSubtopic={activeSubtopic}
              activeSubtopicId={topic.subtopics?.find((s) => s.name === activeSubtopic)?.id || null}
              activeTab="viewpoints"
            />
          </TabsContent>

          <TabsContent value="discussions" className="mt-0 space-y-4">
            <TopicDataLoader
              topicId={topic.id}
              activeSubtopic={activeSubtopic}
              activeSubtopicId={topic.subtopics?.find((s) => s.name === activeSubtopic)?.id || null}
              activeTab="discussions"
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
