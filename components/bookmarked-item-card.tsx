"use client"

import type React from "react"

import { useState, useRef } from "react"
import { MoreHorizontal, GripVertical, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ContentCard } from "@/components/content-card"
import { useToast } from "@/components/ui/use-toast"

interface Collection {
  id: string
  name: string
  itemCount?: number
}

interface BookmarkedItemCardProps {
  id: string // 改為 string 以支援 UUID
  type: string
  title: string
  content: string
  author: {
    name: string
    avatar: string
  }
  timestamp?: string
  topics?: string[]
  tags?: string[]
  subtopics?: string[]
  semanticType?: string
  sourceType?: string
  stats?: {
    likes?: number
    comments?: number
    shares?: number
    replies?: number
    views?: number
    dislikes?: number
  }
  collectionIds: string[] // 改為 string[] 以支援 UUID
  createdAt?: string
  // 新增的 props
  collections?: Collection[]
  onCollectionToggle?: (itemId: string, collectionId: string, isAdd: boolean) => Promise<void>
  onRemoveBookmark?: (itemId: string) => Promise<void>
}

export function BookmarkedItemCard({
  id,
  type,
  title,
  content,
  author,
  timestamp,
  topics = [],
  tags = [],
  subtopics = [],
  semanticType = "default",
  sourceType = "community",
  stats = { likes: 0, comments: 0, shares: 0 },
  collectionIds,
  createdAt,
  collections = [],
  onCollectionToggle,
  onRemoveBookmark,
}: BookmarkedItemCardProps) {
  const { toast } = useToast()
  const [isDragging, setIsDragging] = useState(false)
  const [showQuickAdd, setShowQuickAdd] = useState(false)
  const [isUpdatingCollection, setIsUpdatingCollection] = useState<string | null>(null)
  const [isRemovingBookmark, setIsRemovingBookmark] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const dragPreviewRef = useRef<HTMLDivElement>(null)

  // 獲取所有收藏牆，並標記哪些已被選中
  const getAllCollections = () => {
    return collections.map((collection) => ({
      id: collection.id,
      name: collection.name,
      isSelected: collectionIds.includes(collection.id),
    }))
  }

  // 處理收藏牆切換
  const handleCollectionToggle = async (collectionId: string, isCurrentlySelected: boolean) => {
    if (!onCollectionToggle) return

    try {
      setIsUpdatingCollection(collectionId)
      await onCollectionToggle(id, collectionId, !isCurrentlySelected)
    } catch (error) {
      console.error("切換收藏牆時出錯:", error)
      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsUpdatingCollection(null)
    }
  }

  // 處理移除收藏
  const handleRemoveBookmark = async () => {
    if (!onRemoveBookmark) return

    try {
      setIsRemovingBookmark(true)
      await onRemoveBookmark(id)
      // 移除收藏成功，父組件會處理狀態更新
    } catch (error) {
      console.error("移除收藏時出錯:", error)
      toast({
        title: "移除失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsRemovingBookmark(false)
    }
  }

  // 處理拖動開始
  const handleDragStart = (e: React.DragEvent) => {
    if (!cardRef.current) return

    console.log("卡片拖拽開始，collectionIds:", collectionIds)

    // 設置拖拽數據 - 使用 JSON 格式
    const dragData = JSON.stringify({
      itemId: id, // 現在是 string 格式的 UUID
      itemType: type,
      itemTitle: title,
      collectionIds: collectionIds || [], // 確保 collectionIds 不為 undefined
    })

    console.log("設置拖拽數據:", dragData)

    e.dataTransfer.setData("application/json", dragData)
    e.dataTransfer.setData("text/card-drag", "true") // 添加特殊標識
    e.dataTransfer.effectAllowed = "move"

    setIsDragging(true)

    // 添加全局拖拽樣式並存儲收藏牆信息
    document.body.classList.add("dragging-item")
    cardRef.current.classList.add("dragging-item")
    cardRef.current.setAttribute("data-collection-ids", JSON.stringify(collectionIds || []))
  }

  // 處理拖動結束
  const handleDragEnd = () => {
    setIsDragging(false)

    // 移除全局拖拽樣式
    document.body.classList.remove("dragging-item")
    if (cardRef.current) {
      cardRef.current.classList.remove("dragging-item")
      cardRef.current.removeAttribute("data-collection-ids")
    }
  }

  return (
    <>
      {/* 隱藏的拖拽預覽元素 */}
      <div ref={dragPreviewRef} className="drag-preview" aria-hidden="true" />

      <div
        ref={cardRef}
        className={cn(
          "relative transition-all group",
          isDragging ? "opacity-50 scale-95" : "opacity-100",
          "cursor-grab active:cursor-grabbing",
        )}
        draggable
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onMouseEnter={() => setShowQuickAdd(true)}
        onMouseLeave={() => setShowQuickAdd(false)}
      >
        {/* 拖拽手柄 */}
        <div className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
          <GripVertical className="h-5 w-5 text-muted-foreground" />
        </div>

        {/* 收藏牆標籤 */}
        {collectionIds.length > 0 && (
          <div className="absolute top-2 right-2 z-10">
            <div className="bg-black/30 text-white text-xs px-2 py-1 rounded-md">
              {collectionIds.length > 1 ? `${collectionIds.length} 個收藏牆` : "已收藏"}
            </div>
          </div>
        )}

        {/* 快速添加按鈕 */}
        <div
          className={cn("absolute top-2 right-2 z-20 transition-opacity", showQuickAdd ? "opacity-100" : "opacity-0")}
        >
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8 w-8 rounded-full bg-black/50 hover:bg-black/70 flex items-center justify-center text-white">
              <MoreHorizontal className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>管理收藏</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleRemoveBookmark}
                disabled={isRemovingBookmark}
                className="text-destructive focus:text-destructive"
              >
                {isRemovingBookmark && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                取消收藏
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>添加到收藏牆</DropdownMenuLabel>
              {getAllCollections().length > 0 ? (
                getAllCollections().map((collection) => (
                  <DropdownMenuItem
                    key={collection.id}
                    className="flex items-center"
                    onClick={(e) => e.preventDefault()}
                  >
                    <input
                      type="checkbox"
                      id={`collection-${collection.id}`}
                      checked={collection.isSelected}
                      disabled={isUpdatingCollection === collection.id}
                      className="mr-2"
                      onChange={() => handleCollectionToggle(collection.id, collection.isSelected)}
                    />
                    <label
                      htmlFor={`collection-${collection.id}`}
                      className="flex-1 cursor-pointer"
                    >
                      {collection.name}
                    </label>
                    {isUpdatingCollection === collection.id && (
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    )}
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled className="text-muted-foreground">
                  尚無收藏牆
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 使用 ContentCard 渲染卡片內容 */}
        {type === "viewpoint" ? (
          <ContentCard
            contentType="viewpoint"
            id={id as any} // 保持 UUID 字符串格式，ContentCard 會處理類型轉換
            semanticType={semanticType as any}
            title={title}
            content={content}
            topics={topics}
            subtopics={subtopics || []}
            author={{
              id: `author-${id}`,
              name: author.name,
              avatar: author.avatar,
            }}
            contribution_type={sourceType as any} // 修正屬性名稱
            timestamp={timestamp || createdAt}
            stats={{
              likes: stats.likes || 0,
              dislikes: stats.dislikes || 0,
              comments: stats.comments || 0,
              bookmarks: 0,
            }}
            variant="grid"
          />
        ) : (
          <ContentCard
            contentType="discussion"
            id={id as any} // 保持 UUID 字符串格式，ContentCard 會處理類型轉換
            title={title}
            content={content}
            author={{
              id: `author-${id}`,
              name: author.name,
              avatar: author.avatar,
            }}
            timestamp={timestamp || createdAt || ""}
            tags={tags}
            topics={topics}
            semanticType={semanticType as any}
            stats={{
              replies: stats.replies || 0,
              views: stats.views || 0,
              likes: stats.likes || 0,
              dislikes: stats.dislikes || 0,
            }}
            variant="grid"
          />
        )}
      </div>
    </>
  )
}
