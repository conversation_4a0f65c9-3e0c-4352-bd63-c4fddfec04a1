"use client"

import { useEffect, useState } from "react"
import { ContentCard } from "@/components/content-card"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, Eye } from "lucide-react"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"

interface TopicDataLoaderProps {
  topicId: string
  activeSubtopic: string | null
  activeSubtopicId: string | null
  activeTab: string
}

export function TopicDataLoader({ topicId, activeSubtopic, activeSubtopicId, activeTab }: TopicDataLoaderProps) {
  const [cards, setCards] = useState<any[]>([])
  const [threads, setThreads] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadData() {
      setLoading(true)

      if (activeTab === "viewpoints") {
        try {
          // 使用優化後的 API，一次性獲取所有相關數據
          let apiUrl = `/api/topics/${topicId}/cards?limit=20`
          if (activeSubtopicId) {
            apiUrl = `/api/subtopics/${activeSubtopicId}/cards?limit=20`
          }

          const response = await fetch(apiUrl)
          const result = await response.json()

          if (result.success && result.data) {
            // 轉換數據格式以符合前端組件需求
            const formattedCards = result.data.map((card: any) => ({
              id: card.id,
              title: card.title,
              content: card.content,
              author: {
                id: card.author?.id || "",
                name: card.author?.name || "匿名用戶",
                avatar: card.author?.avatar || "",
              },
              stats: card.stats || { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 },
              contentType: "viewpoint",
              semanticType: card.semantic_type || "concept",
              variant: "grid",
              topics: card.topics?.map((t: any) => t.name) || [],
              subtopics: card.subtopics?.map((s: any) => s.name) || [],
              sourceType: card.contribution_type || "original",
              contribution_type: card.contribution_type || "community",
              originalAuthor: card.original_author || "",
              originalSource: card.original_url || "",
              timestamp: formatTimeAgo(card.created_at),
              features: { truncate: true },
            }))

            setCards(formattedCards)
          } else {
            setCards([])
          }
        } catch (error) {
          console.error("Error loading cards:", error)
          setCards([])
        }
      } else if (activeTab === "discussions") {
        try {
          // 加載討論串 - 也可以優化成類似的批量 API
          const response = await fetch(`/api/topics/${topicId}/threads?limit=20`)
          const result = await response.json()

          if (result.success && result.data) {
            const formattedThreads = result.data.map((thread: any) => ({
              id: thread.id,
              contentType: "discussion",
              variant: "default",
              title: thread.title,
              author: thread.author?.name || "匿名",
              replies: thread.stats?.replies || 0,
              views: thread.stats?.views || Math.floor(Math.random() * 1000), // 暫時使用隨機數
              lastActive: formatTimeAgo(thread.created_at),
            }))

            setThreads(formattedThreads)
          } else {
            setThreads([])
          }
        } catch (error) {
          console.error("Error loading threads:", error)
          setThreads([])
        }
      }

      setLoading(false)
    }

    loadData()
  }, [topicId, activeSubtopic, activeSubtopicId, activeTab])

  // 格式化相對時間的函數
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return `${diffInSeconds}秒前`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分鐘前`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小時前`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}個月前`
    return `${Math.floor(diffInSeconds / 31536000)}年前`
  }

  if (loading) {
    return activeTab === "viewpoints" ? (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden h-full">
            <CardContent className="p-0">
              <div className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-2/3" />
              </div>
              <div className="border-t p-4 flex justify-between">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    ) : (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-7">主題</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-2 text-center">活動</div>
        </div>

        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-4">
              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-7 flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="w-full">
                    <Skeleton className="h-5 w-3/4 mb-1" />
                    <Skeleton className="h-4 w-1/3" />
                  </div>
                </div>
                <div className="col-span-1 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-2 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-2 text-center">
                  <Skeleton className="h-6 w-16 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (activeTab === "viewpoints") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {cards.length > 0 ? (
          cards.map((card) => (
            <div key={card.id} className="relative group h-full">
              <ContentCard {...card} />
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-12">
            <p className="text-muted-foreground">暫無相關觀點卡</p>
          </div>
        )}
      </div>
    )
  } else {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-7">主題</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-2 text-center">活動</div>
        </div>

        {threads.length > 0 ? (
          threads.map((thread) => (
            <Card
              key={thread.id}
              className="overflow-hidden border-border/40 hover:border-border/80 transition-colors hover:shadow-md"
            >
              <CardContent className="p-4">
                <div className="grid grid-cols-12 items-center gap-4">
                  <div className="col-span-7 flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {thread.author.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <Link href={`/thread/${thread.id}`} className="font-medium hover:text-primary transition-colors">
                        {thread.title}
                      </Link>
                      <div className="text-sm text-muted-foreground">{thread.author}</div>
                    </div>
                  </div>
                  <div className="col-span-1 text-center flex justify-center">
                    <Badge variant="outline" className="flex items-center">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      <span>{thread.replies}</span>
                    </Badge>
                  </div>
                  <div className="col-span-2 text-center flex justify-center">
                    <Badge variant="outline" className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      <span>{thread.views >= 1000 ? `${(thread.views / 1000).toFixed(1)}k` : thread.views}</span>
                    </Badge>
                  </div>
                  <div className="col-span-2 text-center">
                    <Badge variant="secondary">{thread.lastActive}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暫無相關討論</p>
          </div>
        )}
      </div>
    )
  }
}
