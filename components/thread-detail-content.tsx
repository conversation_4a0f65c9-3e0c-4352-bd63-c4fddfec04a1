"use client"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>uzzle, Thum<PERSON>Up, ThumbsDown } from "lucide-react"
import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { formatDistanceToNow } from "date-fns"
import { zhTW } from "date-fns/locale"
import type { Thread } from "@/lib/types"
import { addComment, getCommentReferences } from "@/lib/interaction-service"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { Eye, Flag, Share2, Reply, X } from "lucide-react"
import { RichTextContent } from "@/components/rich-text-content"
import { useRouter } from "next/navigation"
import { CardQuote } from "@/components/card-quote"
import { CardMention } from "@/components/card-mention"
import { CardSelector } from "@/components/card-selector"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { OptimizedAvatar } from "@/components/optimized-avatar"

// 討論類型圖標映射
const typeIcons = {
  discussion: <MessageSquare className="h-4 w-4" />,
  question: <Puzzle className="h-4 w-4" />,
  debate: <MessageSquare className="h-4 w-4" />,
  announcement: <Lightbulb className="h-4 w-4" />,
}

// 討論類型名稱映射
const typeNames = {
  discussion: "討論",
  question: "問題",
  debate: "辯論",
  announcement: "公告",
}

// 討論類型顏色映射
const typeColors = {
  discussion: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  question: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  debate: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300",
  announcement: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
}

interface ThreadDetailContentProps {
  thread: Thread
  initialAuthState?: boolean
}

// 截斷文本函數
function truncateText(text: string, maxLength = 150) {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + "..."
}

// 構建回覆樹結構的輔助函數
function buildCommentTree(comments: any[]) {
  const commentMap = new Map()
  const rootComments: any[] = []

  // 首先將所有評論放入 Map 中
  comments.forEach((comment) => {
    comment.children = []
    commentMap.set(comment.id, comment)
  })

  // 然後構建樹結構
  comments.forEach((comment) => {
    if (comment.parent_comment_id) {
      const parent = commentMap.get(comment.parent_comment_id)
      if (parent) {
        parent.children.push(comment)
      } else {
        rootComments.push(comment)
      }
    } else {
      rootComments.push(comment)
    }
  })

  return rootComments
}

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export function ThreadDetailContent({ thread, initialAuthState = false }: ThreadDetailContentProps) {
  const [comment, setComment] = useState("")
  const { user, isAuthenticated, refreshSession } = useAuth()
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const { toast } = useToast()
  const [quotedCard, setQuotedCard] = useState<any | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isClient, setIsClient] = useState(false)
  const commentBoxRef = useRef<HTMLDivElement>(null)
  const supabase = createClientComponentClient()

  // 在現有的 useState 聲明下方添加
  const [likedComments, setLikedComments] = useState<string[]>([])
  const [dislikedComments, setDislikedComments] = useState<string[]>([])
  const [replies, setReplies] = useState(thread.replies || [])
  const [commentReferences, setCommentReferences] = useState<Record<string, any>>({})
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [quotedContent, setQuotedContent] = useState<string>("")
  const [collapsedComments, setCollapsedComments] = useState<string[]>([])
  const [commentReactionData, setCommentReactionData] = useState<{
    counts: Record<string, { likes: number; dislikes: number }>
    userReactions: Record<string, { liked: boolean; disliked: boolean }>
  }>({ counts: {}, userReactions: {} })

  const [likeCount, setLikeCount] = useState(thread.likes || 0)
  const [dislikeCount, setDislikeCount] = useState(thread.dislikes || 0)
  const [hasLiked, setHasLiked] = useState(false)
  const [hasDisliked, setHasDisliked] = useState(false)
  const [isProcessingReaction, setIsProcessingReaction] = useState(false)
  const [pendingReactions, setPendingReactions] = useState<Set<string>>(new Set())
  const [showCardMention, setShowCardMention] = useState(false)

  // 在組件頂部添加 router
  const router = useRouter()

  // 在客戶端渲染後設置 isClient 為 true
  useEffect(() => {
    setIsClient(true)

    // 如果服務器端和客戶端的認證狀態不一致，刷新會話
    if (initialAuthState !== isAuthenticated) {
      refreshSession().catch(console.error)
    }
  }, [initialAuthState, isAuthenticated, refreshSession])

  // 獲取評論的引用卡片
  useEffect(() => {
    if (replies.length > 0) {
      const fetchCommentReferences = async () => {
        const commentIds = replies.map((c) => c.id)
        const { success, data } = await getCommentReferences(commentIds)
        if (success && data) {
          setCommentReferences(data)
        }
      }

      fetchCommentReferences()
    }
  }, [replies])

  // 批量獲取評論的反應狀態
  useEffect(() => {
    if (!isClient || replies.length === 0) return

    const fetchBatchReactions = async () => {
      try {
        const commentIds = replies.map((comment) => comment.id)
        const response = await fetch("/api/comments/reactions/batch", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ commentIds }),
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setCommentReactionData(data.data)

            // 更新 liked/disliked 狀態
            if (isAuthenticated && data.data.userReactions) {
              const likedIds = Object.entries(data.data.userReactions)
                .filter(([_, reactions]) => reactions.liked)
                .map(([id]) => id)
              const dislikedIds = Object.entries(data.data.userReactions)
                .filter(([_, reactions]) => reactions.disliked)
                .map(([id]) => id)

              setLikedComments(likedIds)
              setDislikedComments(dislikedIds)
            }
          }
        }
      } catch (error) {
        console.error("Error fetching batch reactions:", error)
      }
    }

    fetchBatchReactions()
  }, [replies, isClient, isAuthenticated])

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: zhTW })
    } catch (error) {
      return dateString
    }
  }

  // 處理卡片引用
  const handleQuoteCard = (card: any) => {
    setQuotedCard(card)

    // 聚焦到文本框
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  // 處理卡片選擇 (從 CardMention 組件)
  const handleSelectCard = (selectedCard: any) => {
    setQuotedCard(selectedCard)
    setShowCardMention(false)

    // 聚焦到文本框
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  // 清除卡片引用
  const clearCardQuote = () => {
    setQuotedCard(null)
  }

  // 獲取評論內容
  const getCommentContent = (commentId: string | null) => {
    if (!commentId) return ""
    const comment = replies.find((c) => c.id === commentId)
    return comment ? comment.content : ""
  }

  // 獲取評論作者
  const getCommentAuthor = (commentId: string | null) => {
    if (!commentId) return null
    const comment = replies.find((c) => c.id === commentId)
    return comment ? comment.author.name : null
  }

  // 處理回覆某人
  const handleReplyTo = (commentId: string) => {
    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能回覆",
        variant: "destructive",
      })
      return
    }

    const content = getCommentContent(commentId)
    setReplyingTo(replyingTo === commentId ? null : commentId)
    setQuotedContent(replyingTo === commentId ? "" : content)

    // 滾動到評論框
    if (commentBoxRef.current) {
      setTimeout(() => {
        commentBoxRef.current?.scrollIntoView({ behavior: "smooth" })
        // 聚焦到文本框
        if (textareaRef.current) {
          textareaRef.current.focus()
        }
      }, 100)
    }
  }

  // 清除引用
  const clearQuote = () => {
    setQuotedContent("")
    // 不要改變replyingTo狀態，這樣用戶仍然可以回覆同一個人，只是沒有引用內容
  }

  // 處理摺疊/展開回覆
  const toggleCollapseComment = (commentId: string) => {
    setCollapsedComments((prev) =>
      prev.includes(commentId) ? prev.filter((id) => id !== commentId) : [...prev, commentId],
    )
  }

  // 處理提交留言
  const handleSubmitComment = async () => {
    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能留言",
        variant: "destructive",
      })
      return
    }

    if (!comment.trim()) {
      toast({
        title: "留言不能為空",
        variant: "destructive",
      })
      return
    }

    setIsSubmittingComment(true)
    try {
      const result = await addComment({
        itemType: "thread", // This is correct as it matches the expected enum values in the function
        itemId: thread.id,
        content: comment,
        parentCommentId: replyingTo,
        referencedCard: quotedCard ? { id: quotedCard.id, type: "quote" } : null,
      })

      if (result.success && result.data) {
        // 更新評論列表
        const newComment = result.data
        setReplies((prev) => [...prev, newComment])

        // 如果有引用卡片，更新引用映射
        if (result.data.referenced_card) {
          setCommentReferences((prev) => ({
            ...prev,
            [newComment.id]: result.data.referenced_card,
          }))
        }

        setComment("")
        setQuotedContent("")
        setReplyingTo(null)
        setQuotedCard(null)
        toast({
          title: "留言已提交",
          description: "您的留言已成功提交",
        })
      } else {
        toast({
          title: "留言提交失敗",
          description: result.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error submitting comment:", error)
      toast({
        title: "留言提交失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsSubmittingComment(false)
    }
  }

  // 處理點讚/不認同 - 直接使用 Supabase 客戶端
  const handleReaction = async (reactionType: "like" | "dislike", e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行此操作",
        variant: "destructive",
      })
      return
    }

    // 檢查是否正在處理相同的反應類型
    if (pendingReactions.has(reactionType)) {
      return
    }

    // 確保 id 是字符串並檢查格式
    const itemId = typeof thread.id === "number" ? thread.id.toString() : thread.id
    if (!isValidUUID(itemId)) {
      console.log("跳過反應操作：無效的 UUID 格式", itemId)
      return
    }

    // 添加到待處理集合
    setPendingReactions((prev) => new Set(prev).add(reactionType))

    // 保存當前狀態，以便在失敗時恢復
    const wasLiked = hasLiked
    const wasDisliked = hasDisliked
    const prevLikeCount = likeCount
    const prevDislikeCount = dislikeCount

    // 樂觀更新 UI
    if (reactionType === "like") {
      setHasLiked(!wasLiked)
      setLikeCount((prev) => (wasLiked ? Math.max(0, prev - 1) : prev + 1))
      if (hasDisliked) {
        setHasDisliked(false)
        setDislikeCount((prev) => Math.max(0, prev - 1))
      }
    } else {
      setHasDisliked(!wasDisliked)
      setDislikeCount((prev) => (wasDisliked ? Math.max(0, prev - 1) : prev + 1))
      if (hasLiked) {
        setHasLiked(false)
        setLikeCount((prev) => Math.max(0, prev - 1))
      }
    }

    try {
      // 使用 API 路由而不是直接使用 Supabase 客戶端
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: "thread",
          itemId: itemId,
          reactionType,
        }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        // 如果請求失敗，恢復原來的狀態
        setHasLiked(wasLiked)
        setHasDisliked(wasDisliked)
        setLikeCount(prevLikeCount)
        setDislikeCount(prevDislikeCount)

        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling reaction:", error)

      // 如果請求失敗，恢復原來的狀態
      setHasLiked(wasLiked)
      setHasDisliked(wasDisliked)
      setLikeCount(prevLikeCount)
      setDislikeCount(prevDislikeCount)

      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      // 從待處理集合中移除此操作
      setPendingReactions((prev) => {
        const newSet = new Set(prev)
        newSet.delete(reactionType)
        return newSet
      })
    }
  }

  // 處理留言點讚/倒讚
  const handleCommentReaction = async (commentId: string, reactionType: "like" | "dislike") => {
    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能點讚/倒讚",
        variant: "destructive",
      })
      return
    }

    // 樂觀更新 UI
    if (reactionType === "like") {
      // 如果已經倒讚，先取消倒讚
      if (dislikedComments.includes(commentId)) {
        setDislikedComments(dislikedComments.filter((id) => id !== commentId))
      }

      // 切換點讚狀態
      setLikedComments(
        likedComments.includes(commentId)
          ? likedComments.filter((id) => id !== commentId)
          : [...likedComments, commentId],
      )
    } else {
      // 如果已經點讚，先取消點讚
      if (likedComments.includes(commentId)) {
        setLikedComments(likedComments.filter((id) => id !== commentId))
      }

      // 切換倒讚狀態
      setDislikedComments(
        dislikedComments.includes(commentId)
          ? dislikedComments.filter((id) => id !== commentId)
          : [...dislikedComments, commentId],
      )
    }

    try {
      // 使用 API 路由而不是直接使用 Supabase 客戶端
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: "comment",
          itemId: commentId,
          reactionType,
        }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        // 如果請求失敗，恢復原來的狀態
        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling comment reaction:", error)

      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      // 更新本地計數
      setCommentReactionData((prev) => {
        const newData = { ...prev }
        if (!newData.counts[commentId]) {
          newData.counts[commentId] = { likes: 0, dislikes: 0 }
        }

        if (reactionType === "like") {
          if (likedComments.includes(commentId)) {
            newData.counts[commentId].likes = Math.max(0, newData.counts[commentId].likes - 1)
          } else {
            newData.counts[commentId].likes++
            if (dislikedComments.includes(commentId)) {
              newData.counts[commentId].dislikes = Math.max(0, newData.counts[commentId].dislikes - 1)
            }
          }
        } else {
          if (dislikedComments.includes(commentId)) {
            newData.counts[commentId].dislikes = Math.max(0, newData.counts[commentId].dislikes - 1)
          } else {
            newData.counts[commentId].dislikes++
            if (likedComments.includes(commentId)) {
              newData.counts[commentId].likes = Math.max(0, newData.counts[commentId].likes - 1)
            }
          }
        }

        return newData
      })
    }
  }

  // 獲取用戶反應狀態
  useEffect(() => {
    if (!isAuthenticated || !isClient) return

    const fetchReactionStatus = async () => {
      try {
        // 確保 id 是字符串並檢查格式
        const itemId = typeof thread.id === "number" ? thread.id.toString() : thread.id
        if (!isValidUUID(itemId)) {
          console.log("跳過反應狀態檢查：無效的 UUID 格式", itemId)
          return
        }

        // 檢查用戶是否已點讚
        const likeRes = await fetch(`/api/reactions/check?itemType=thread&itemId=${itemId}&reactionType=like`)

        // 檢查響應是否成功
        if (!likeRes.ok) {
          console.error("Error checking like status:", await likeRes.text())
          return
        }

        const likeData = await likeRes.json()
        if (likeData.success) {
          setHasLiked(!!likeData.data.hasReacted)
        }

        // 檢查用戶是否已不認同
        const dislikeRes = await fetch(`/api/reactions/check?itemType=thread&itemId=${itemId}&reactionType=dislike`)

        // 檢查響應是否成功
        if (!dislikeRes.ok) {
          console.error("Error checking dislike status:", await dislikeRes.text())
          return
        }

        const dislikeData = await dislikeRes.json()
        if (dislikeData.success) {
          setHasDisliked(!!dislikeData.data.hasReacted)
        }
      } catch (error) {
        console.error("Error checking reaction status:", error)
      }
    }

    fetchReactionStatus()
  }, [thread.id, isAuthenticated, isClient])

  // 監聽文本框輸入，檢測 @ 符號
  useEffect(() => {
    const handleInput = () => {
      if (textareaRef.current) {
        const text = textareaRef.current.value
        const cursorPosition = textareaRef.current.selectionStart
        const textBeforeCursor = text.substring(0, cursorPosition)

        // 檢查是否有 @ 符號
        const atIndex = textBeforeCursor.lastIndexOf("@")
        if (atIndex !== -1 && (atIndex === 0 || /\s/.test(textBeforeCursor[atIndex - 1]))) {
          // 提取 @ 後面的搜索詞
          const searchText = textBeforeCursor.substring(atIndex + 1)

          // 如果有 @ 且後面沒有空格，則顯示卡片選擇菜單
          if (!searchText.includes(" ")) {
            setShowCardMention(true)
          } else {
            setShowCardMention(false)
          }
        } else {
          setShowCardMention(false)
        }
      }
    }

    const textarea = textareaRef.current
    if (textarea) {
      textarea.addEventListener("input", handleInput)
      return () => {
        textarea.removeEventListener("input", handleInput)
      }
    }
  }, [])

  // 構建評論樹
  const commentTree = buildCommentTree([...replies])

  // 渲染單個評論及其子評論
  const renderComment = (comment: any, index: number, level = 0) => {
    const isCollapsed = collapsedComments.includes(comment.id)
    const hasChildren = comment.children && comment.children.length > 0
    const referencedCard = commentReferences[comment.id] || comment.quotedCard || null

    return (
      <div key={comment.id} id={`comment-${comment.id}`} className="relative">
        <Card
          className={cn(
            "mb-3",
            level > 0 && "ml-6 border-l-4 border-l-primary/20 relative",
            level > 1 && "ml-12",
            level > 2 && "ml-16",
            level > 3 && "ml-20",
          )}
        >
          {level > 0 && <div className="absolute -left-[2px] top-0 bottom-0 w-1 bg-primary/20"></div>}
          <CardContent className={cn("p-4", level > 0 && "border-l-4 border-l-primary/10")}>
            <div className="flex items-start gap-3">
              <OptimizedAvatar
                src={comment.author?.avatar}
                alt={comment.author?.name || "未知"}
                fallback={comment.author?.name || "未知"}
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="font-medium">{comment.author?.name || "未知"}</div>
                    {level === 0 && (
                      <div className="text-xs px-2 py-0.5 bg-primary text-primary-foreground font-medium rounded-full">
                        #{index + 1}樓
                      </div>
                    )}
                    {comment.parent_comment_id && (
                      <div className="text-xs text-muted-foreground">
                        回覆 <span className="font-medium">{getCommentAuthor(comment.parent_comment_id)}</span>
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(comment.createdAt || comment.created_at)}
                  </div>
                </div>

                {/* 顯示引用的卡片 */}
                {referencedCard && (
                  <div className="mt-3 mb-4">
                    <CardQuote card={referencedCard} className="border-l-primary/70" />
                  </div>
                )}

                <div className="mt-2 text-sm whitespace-pre-line">{comment.content}</div>

                {/* 評論點讚倒讚和回覆按鈕 */}
                <div className="mt-3 flex items-center gap-2">
                  {isClient && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 h-7 px-2 text-xs",
                          likedComments.includes(comment.id) && "text-primary",
                        )}
                        onClick={() => handleCommentReaction(comment.id, "like")}
                      >
                        <ThumbsUp className={cn("h-3.5 w-3.5", likedComments.includes(comment.id) && "fill-current")} />
                        <span>{commentReactionData.counts[comment.id]?.likes || 0}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 h-7 px-2 text-xs",
                          dislikedComments.includes(comment.id) && "text-primary",
                        )}
                        onClick={() => handleCommentReaction(comment.id, "dislike")}
                      >
                        <ThumbsDown
                          className={cn("h-3.5 w-3.5", dislikedComments.includes(comment.id) && "fill-current")}
                        />
                        <span>{commentReactionData.counts[comment.id]?.dislikes || 0}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 h-7 px-2 text-xs"
                        onClick={() => handleReplyTo(comment.id)}
                      >
                        <Reply className="h-3.5 w-3.5" />
                        <span>回覆</span>
                      </Button>
                    </>
                  )}

                  {hasChildren && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex items-center gap-1 h-7 px-2 text-xs"
                      onClick={() => toggleCollapseComment(comment.id)}
                    >
                      {isCollapsed ? (
                        <>
                          <span>展開 {comment.children.length} 則回覆</span>
                        </>
                      ) : (
                        <>
                          <span>收起回覆</span>
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {hasChildren && (
          <div className={cn("relative pl-6", isCollapsed && "hidden")}>
            {/* 添加連接線 */}
            <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-primary/10"></div>

            {comment.children.map((childComment: any, childIndex: number) =>
              renderComment(childComment, childIndex, level + 1),
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-[1400px] mx-auto px-4 space-y-6">
      {/* 麵包屑導航 */}
      <nav className="flex items-center text-sm text-muted-foreground mb-4">
        <Link href="/" className="hover:text-foreground flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          首頁
        </Link>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 mx-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <span className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">討論串</span>
      </nav>

      {/* 討論內容 */}
      <Card className="overflow-hidden bg-gray-50 dark:bg-gray-900">
        <CardHeader className="p-6 pb-4">
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {/* 類型標籤 */}
            <Badge
              variant="outline"
              className={cn(
                "flex items-center gap-1",
                typeColors[thread.semantic_type as keyof typeof typeColors] || typeColors.discussion,
              )}
            >
              {typeIcons[thread.semantic_type as keyof typeof typeIcons] || typeIcons.discussion}
              <span>{typeNames[thread.semantic_type as keyof typeof typeNames] || "討論"}</span>
            </Badge>
          </div>

          {/* 標題 */}
          <h1 className="text-3xl font-bold mb-4">{thread.title}</h1>

          {/* 作者和日期 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <OptimizedAvatar
                src={thread.author?.avatar}
                alt={thread.author?.name || "未知"}
                fallback={thread.author?.name || "未知"}
              />
              <div>
                <div className="font-medium">{thread.author?.name || "未知"}</div>
                <div className="text-sm text-muted-foreground">發布於: {formatDate(thread.created_at)}</div>
              </div>
            </div>

            {/* 統計數據 */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1" title="觀看次數">
                <Eye className="h-4 w-4" />
                <span>{thread.views || 0}</span>
              </div>
              <div className="flex items-center gap-1" title="留言">
                <MessageSquare className="h-4 w-4" />
                <span>{replies.length || 0}</span>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 pt-2">
          {/* 內容 - 使用富文本顯示 */}
          <div className="text-base mb-6 leading-relaxed">
            <RichTextContent content={thread.content} />
          </div>
        </CardContent>

        <CardFooter className="p-6 pt-0 flex flex-col gap-4">
          {/* 操作按鈕 */}
          <div className="w-full pt-4 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* 點讚倒讚按鈕 */}
                {isClient && (
                  <>
                    <Button
                      variant={hasLiked ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        "flex items-center gap-2",
                        hasLiked && "bg-primary text-primary-foreground",
                        pendingReactions.has("like") && "opacity-70",
                      )}
                      onClick={(e) => handleReaction("like", e)}
                    >
                      <ThumbsUp className={cn("h-4 w-4", hasLiked && "fill-current")} />
                      <span>{likeCount}</span>
                    </Button>
                    <Button
                      variant={hasDisliked ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        "flex items-center gap-2",
                        hasDisliked && "bg-primary text-primary-foreground",
                        pendingReactions.has("dislike") && "opacity-70",
                      )}
                      onClick={(e) => handleReaction("dislike", e)}
                    >
                      <ThumbsDown className={cn("h-4 w-4", hasDisliked && "fill-current")} />
                      <span>{dislikeCount}</span>
                    </Button>
                  </>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Share2 className="h-4 w-4" />
                  <span>分享</span>
                </Button>

                <Button variant="ghost" size="sm" className="flex items-center gap-2 text-muted-foreground">
                  <Flag className="h-4 w-4" />
                  <span>檢舉</span>
                </Button>
              </div>
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* 回覆區 */}
      <div className="space-y-4 pt-4" id="comments">
        <div className="flex justify-between items-center border-b pb-1 mb-4">
          <h2 className="text-xl font-bold flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            回覆區
          </h2>
        </div>

        {/* 回覆列表 */}
        <div className="space-y-4">
          {commentTree.length > 0 ? (
            commentTree.map((comment, index) => renderComment(comment, index))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>暫無回覆，成為第一個回覆的人吧！</p>
            </div>
          )}
        </div>

        {/* 添加回覆 */}
        <div className="pt-4" ref={commentBoxRef}>
          <h3 className="text-sm font-medium mb-2">
            {replyingTo !== null
              ? `回覆 ${getCommentAuthor(replyingTo)}`
              : isAuthenticated
                ? "添加回覆"
                : "請先登入..."}
          </h3>

          {/* 引用內容區域 */}
          {quotedContent && (
            <div className="mb-4 relative">
              <div className="bg-muted p-3 rounded-md border-l-4 border-primary/50 text-sm text-muted-foreground">
                <button
                  type="button"
                  className="absolute top-1 right-1 h-5 w-5 p-0 flex items-center justify-center text-muted-foreground hover:text-foreground"
                  onClick={clearQuote}
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-medium mb-1">引用 {getCommentAuthor(replyingTo)} 的發言：</p>
                <p className="whitespace-pre-line">{truncateText(quotedContent, 200)}</p>
              </div>
            </div>
          )}

          {/* 引用卡片區域 */}
          {quotedCard && (
            <div className="mb-4 relative">
              <div className="bg-muted p-3 rounded-md border-l-4 border-primary/50">
                <button
                  type="button"
                  className="absolute top-1 right-1 h-5 w-5 p-0 flex items-center justify-center text-muted-foreground hover:text-foreground"
                  onClick={clearCardQuote}
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-medium mb-1 text-sm">引用卡片：</p>
                <CardQuote card={quotedCard} className="border-l-primary/70" />
              </div>
            </div>
          )}

          {replyingTo !== null && (
            <div className="mb-2 flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => {
                  setReplyingTo(null)
                  setQuotedContent("")
                }}
              >
                取消回覆
              </Button>
            </div>
          )}

          <div className="relative">
            <Textarea
              ref={textareaRef}
              placeholder={isAuthenticated ? "分享你的想法... 使用 @ 可以引用卡片" : "請先登入..."}
              className="min-h-[100px] mb-2"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              disabled={!isAuthenticated || isSubmittingComment}
            />

            {/* 卡片選擇菜單 */}
            {showCardMention && (
              <div className="relative">
                <CardMention textareaRef={textareaRef} onSelectCard={handleSelectCard} />
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button onClick={handleSubmitComment} disabled={!comment.trim() || !isAuthenticated || isSubmittingComment}>
              {isSubmittingComment ? "發布中..." : !isAuthenticated ? "請先登入" : "發布回覆"}
            </Button>
            {isAuthenticated && <CardSelector onSelectCard={handleSelectCard} buttonText="引用卡片" />}
            <div className="text-xs text-muted-foreground">
              提示：使用 <span className="font-medium">@</span> 可以引用卡片
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
