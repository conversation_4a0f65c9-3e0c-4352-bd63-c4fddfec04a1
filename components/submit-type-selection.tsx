"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { FileText, Users, MessageCircle } from "lucide-react"

// 更新 interface 添加 discussion 類型
interface SubmitTypeSelectionProps {
  onSelect: (type: "original" | "others" | "discussion") => void
}

export function SubmitTypeSelection({ onSelect }: SubmitTypeSelectionProps) {
  const [hoveredCard, setHoveredCard] = useState<"original" | "others" | "discussion" | null>(null)

  return (
    <div className="flex flex-col items-center justify-center py-8">
      <h2 className="text-2xl font-bold mb-2 text-center">你是要發表...</h2>
      <p className="text-muted-foreground mb-8 text-center">選擇你要分享的內容類型</p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-6xl">
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onHoverStart={() => setHoveredCard("original")}
          onHoverEnd={() => setHoveredCard(null)}
        >
          <Card
            className={`h-64 cursor-pointer border-2 transition-colors overflow-hidden ${
              hoveredCard === "original" ? "border-primary" : "border-border/40"
            }`}
            onClick={() => onSelect("original")}
          >
            <CardContent className="p-0 h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 text-center relative">
                <div
                  className={`absolute inset-0 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 opacity-${
                    hoveredCard === "original" ? "100" : "70"
                  } transition-opacity`}
                />
                <div className="relative z-10 flex flex-col items-center">
                  <div
                    className={`rounded-full p-4 mb-4 bg-amber-100 dark:bg-amber-900/30 ${
                      hoveredCard === "original"
                        ? "text-amber-600 dark:text-amber-400"
                        : "text-amber-500 dark:text-amber-500"
                    }`}
                  >
                    <FileText size={40} />
                  </div>
                  <h3 className="text-2xl font-bold mb-2">原創內容</h3>
                  <p className="text-muted-foreground max-w-xs">分享你自己的觀點、經驗或實作案例，幫助社群共同成長</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onHoverStart={() => setHoveredCard("others")}
          onHoverEnd={() => setHoveredCard(null)}
        >
          <Card
            className={`h-64 cursor-pointer border-2 transition-colors overflow-hidden ${
              hoveredCard === "others" ? "border-primary" : "border-border/40"
            }`}
            onClick={() => onSelect("others")}
          >
            <CardContent className="p-0 h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 text-center relative">
                <div
                  className={`absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 opacity-${
                    hoveredCard === "others" ? "100" : "70"
                  } transition-opacity`}
                />
                <div className="relative z-10 flex flex-col items-center">
                  <div
                    className={`rounded-full p-4 mb-4 bg-blue-100 dark:bg-blue-900/30 ${
                      hoveredCard === "others" ? "text-blue-600 dark:text-blue-400" : "text-blue-500 dark:text-blue-500"
                    }`}
                  >
                    <Users size={40} />
                  </div>
                  <h3 className="text-2xl font-bold mb-2">他人觀點</h3>
                  <p className="text-muted-foreground max-w-xs">分享他人的優質內容，幫助更多人發現有價值的資訊</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onHoverStart={() => setHoveredCard("discussion")}
          onHoverEnd={() => setHoveredCard(null)}
        >
          <Card
            className={`h-64 cursor-pointer border-2 transition-colors overflow-hidden ${
              hoveredCard === "discussion" ? "border-primary" : "border-border/40"
            }`}
            onClick={() => onSelect("discussion")}
          >
            <CardContent className="p-0 h-full">
              <div className="flex flex-col items-center justify-center h-full p-6 text-center relative">
                <div
                  className={`absolute inset-0 bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-950/30 dark:to-teal-950/30 opacity-${
                    hoveredCard === "discussion" ? "100" : "70"
                  } transition-opacity`}
                />
                <div className="relative z-10 flex flex-col items-center">
                  <div
                    className={`rounded-full p-4 mb-4 bg-green-100 dark:bg-green-900/30 ${
                      hoveredCard === "discussion"
                        ? "text-green-600 dark:text-green-400"
                        : "text-green-500 dark:text-green-500"
                    }`}
                  >
                    <MessageCircle size={40} />
                  </div>
                  <h3 className="text-2xl font-bold mb-2">發起討論</h3>
                  <p className="text-muted-foreground max-w-xs">提出問題或議題，邀請社群成員一起討論和交流想法</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
