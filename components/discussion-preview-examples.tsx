"use client"

import { useState } from "react"
import { ContentCard } from "@/components/content-card"
import { Button } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"

// 示例討論數據
const sampleDiscussions = [
  {
    id: 1,
    semanticType: "discussion" as const,
    title: "人工智能在醫療領域的應用前景如何？",
    content:
      "隨著人工智能技術的快速發展，它在醫療領域的應用也越來越廣泛。從輔助診斷到藥物研發，AI似乎正在改變醫療行業的方方面面。但是，AI在醫療領域的應用也面臨著數據隱私、算法透明度等挑戰。大家對此有什麼看法？",
    author: {
      id: "user1",
      name: "醫學研究者",
      avatar: "/diverse-research-team.png",
    },
    timestamp: "2小時前",
    tags: ["人工智能", "醫療科技", "倫理討論"],
    topics: ["AI應用"],
    stats: {
      replies: 24,
      views: 156,
      likes: 42,
    },
    isHot: true,
  },
  {
    id: 2,
    semanticType: "question" as const,
    title: "如何有效提高英語口語水平？",
    content:
      "我已經學習英語多年，但口語表達仍然是我的弱項。我嘗試過看英語電影、聽播客等方法，但進步不明顯。有沒有什麼更有效的方法可以提高英語口語水平？特別是對於已經有一定基礎但需要突破的學習者。",
    author: {
      id: "user2",
      name: "語言學習者",
      avatar: "/mystical-forest-spirit.png",
    },
    timestamp: "昨天",
    tags: ["英語學習", "語言技巧", "自學方法"],
    topics: ["語言學習"],
    stats: {
      replies: 18,
      views: 89,
      likes: 15,
    },
    isNew: true,
  },
  {
    id: 3,
    semanticType: "brainstorm" as const,
    title: "遠程工作的未來趨勢",
    content:
      "疫情加速了遠程工作的普及，許多公司發現遠程工作模式不僅可行，而且在某些方面還提高了效率。隨著技術的發展和工作觀念的改變，遠程工作是否會成為未來的主流工作方式？它將如何改變我們的職業生涯和生活方式？",
    author: {
      id: "user3",
      name: "職場觀察家",
      avatar: "/modern-architect-studio.png",
    },
    timestamp: "3天前",
    tags: ["遠程工作", "職場趨勢", "數字游民"],
    topics: ["工作趨勢"],
    stats: {
      replies: 32,
      views: 215,
      likes: 67,
    },
  },
  {
    id: 4,
    semanticType: "chat" as const,
    title: "分享你最近看的一本好書",
    content:
      "最近讀了什麼好書？不限類型，可以是小說、非虛構類、技術書籍等。分享一下書名、作者和簡短的感受。我最近讀完了《原子習慣》，覺得裡面關於習慣養成的方法非常實用，已經開始嘗試應用到日常生活中了。",
    author: {
      id: "user4",
      name: "書蟲一枚",
      avatar: "",
    },
    timestamp: "1週前",
    tags: ["閱讀", "書籍推薦", "生活分享"],
    topics: ["閱讀"],
    stats: {
      replies: 45,
      views: 230,
      likes: 38,
    },
  },
]

// 將樣本數據轉換為 ContentCardProps 格式
const contentCards = sampleDiscussions.map((discussion) => ({
  contentType: "discussion" as const,
  ...discussion,
}))

export function DiscussionPreviewExamples() {
  const [variant, setVariant] = useState<"card" | "compact" | "grid" | "quote">("card")
  const [columns, setColumns] = useState<1 | 2 | 3>(2)

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-4 items-center">
        <div className="space-x-2">
          <Button variant={variant === "card" ? "default" : "outline"} onClick={() => setVariant("card")}>
            卡片式
          </Button>
          <Button variant={variant === "compact" ? "default" : "outline"} onClick={() => setVariant("compact")}>
            精簡式
          </Button>
          <Button variant={variant === "grid" ? "default" : "outline"} onClick={() => setVariant("grid")}>
            網格式
          </Button>
          <Button variant={variant === "quote" ? "default" : "outline"} onClick={() => setVariant("quote")}>
            引用式
          </Button>
        </div>

        {variant === "grid" && (
          <>
            <Separator orientation="vertical" className="h-8" />
            <div className="space-x-2">
              <Button variant={columns === 1 ? "default" : "outline"} onClick={() => setColumns(1)} size="sm">
                1列
              </Button>
              <Button variant={columns === 2 ? "default" : "outline"} onClick={() => setColumns(2)} size="sm">
                2列
              </Button>
              <Button variant={columns === 3 ? "default" : "outline"} onClick={() => setColumns(3)} size="sm">
                3列
              </Button>
            </div>
          </>
        )}
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="discussion">討論</TabsTrigger>
          <TabsTrigger value="question">問題</TabsTrigger>
          <TabsTrigger value="brainstorm">集思廣益</TabsTrigger>
          <TabsTrigger value="chat">閒聊</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          {variant === "grid" ? (
            <div
              className={`grid gap-4 ${
                columns === 1
                  ? "grid-cols-1"
                  : columns === 2
                    ? "grid-cols-1 md:grid-cols-2"
                    : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
              }`}
            >
              {contentCards.map((card) => (
                <ContentCard key={card.id} {...card} variant={variant} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {contentCards.map((card) => (
                <ContentCard key={card.id} {...card} variant={variant} />
              ))}
            </div>
          )}
        </TabsContent>

        {["discussion", "question", "brainstorm", "chat"].map((type) => (
          <TabsContent key={type} value={type} className="mt-0">
            <div className={variant === "grid" ? "grid gap-4 grid-cols-1 md:grid-cols-2" : "space-y-4"}>
              {contentCards
                .filter((card) => card.semanticType === type)
                .map((card) => (
                  <ContentCard key={card.id} {...card} variant={variant} />
                ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
