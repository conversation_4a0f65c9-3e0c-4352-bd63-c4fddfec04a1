import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)
        const searchParams = request.nextUrl.searchParams

        const includeSubtopics = searchParams.get("includeSubtopics") === "true"
        const limit = Number.parseInt(searchParams.get("limit") || "100", 10)

        let query = supabase
            .from("topics")
            .select(`
                id,
                name,
                slug,
                description
                ${includeSubtopics ? `, subtopics:subtopics!topic_id(id, name, slug)` : ""}
            `)
            .order("name")
            .limit(limit)

        const { data: topics, error } = await query

        if (error) {
            console.error("Error fetching topics:", error)
            return NextResponse.json(
                { success: false, error: "Failed to fetch topics" },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: topics || [],
        })
    } catch (error) {
        console.error("Topics API error:", error)
        return NextResponse.json(
            { success: false, error: "Internal server error" },
            { status: 500 }
        )
    }
} 