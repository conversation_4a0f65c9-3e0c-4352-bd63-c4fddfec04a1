import { NextRequest, NextResponse } from "next/server"
import { getCardsByTopic } from "@/lib/topic-card-service"
import { memoryCache, generateCacheKey } from "@/lib/cache-service"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ topicId: string }> }
) {
  try {
    const { topicId } = await params
    const searchParams = request.nextUrl.searchParams
    const limit = Number.parseInt(searchParams.get("limit") || "20", 10)

    // 生成快取鍵
    const cacheKey = generateCacheKey(`topic-cards:${topicId}`, { limit })

    // 檢查快取
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return NextResponse.json(cached)
    }

    // 使用現有服務但添加快取
    const response = await getCardsByTopic(topicId, limit)

    // 快取結果
    if (response.success) {
      memoryCache.set(cacheKey, response, 5 * 60 * 1000) // 5分鐘快取
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching cards by topic:", error)
    return NextResponse.json(
      { success: false, data: null, error: "Failed to fetch cards" },
      { status: 500 }
    )
  }
}
