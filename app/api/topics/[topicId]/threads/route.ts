import { NextResponse } from "next/server"
import { getThreadsByTopic } from "@/lib/thread-service"

export async function GET(request: Request, { params }: { params: Promise<{ topicId: string }> }) {
  try {
    const { topicId } = await params
    const response = await getThreadsByTopic(topicId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching threads by topic:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch threads" }, { status: 500 })
  }
}
