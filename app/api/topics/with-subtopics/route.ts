import { NextResponse } from "next/server"
import { getAllTopicsWithSubtopics } from "@/lib/topic-service"

export async function GET() {
  try {
    const response = await getAllTopicsWithSubtopics()

    if (!response.success) {
      return NextResponse.json({ error: response.error }, { status: 400 })
    }

    return NextResponse.json(response.data)
  } catch (error) {
    console.error("Error fetching topics with subtopics:", error)
    return NextResponse.json({ error: "Failed to fetch topics" }, { status: 500 })
  }
}
