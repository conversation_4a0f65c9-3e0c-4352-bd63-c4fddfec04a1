import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const body = await request.json()
        const {
            title,
            content,
            semanticType,
            topicIds,
            subtopicIds,
            status = "published", // 新增 status 參數，討論預設為已發布
        } = body

        // 驗證必要參數
        if (!title || !content || !semanticType) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：標題、內容和語義類型"
            }, { status: 400 })
        }

        // 1. 創建討論串
        const { data: thread, error: threadError } = await supabase
            .from("threads")
            .insert({
                title,
                content,
                author_id: user.id,
                semantic_type: semanticType,
                status: status, // 使用傳入的 status
                published_at: status === "published" ? new Date().toISOString() : null, // 如果是已發布狀態，設定發布時間
            })
            .select()
            .single()

        if (threadError) {
            console.error("創建討論串時出錯:", threadError)
            return NextResponse.json({
                success: false,
                error: "創建討論串失敗"
            }, { status: 500 })
        }

        // 2. 添加主題關聯
        if (topicIds && topicIds.length > 0) {
            const topicRelations = topicIds.map((topicId: string) => ({
                thread_id: thread.id,
                topic_id: topicId,
            }))

            const { error: topicError } = await supabase
                .from("thread_topics")
                .insert(topicRelations)

            if (topicError) {
                console.error("添加主題關聯時出錯:", topicError)
                // 這裡可以選擇回滾討論串創建或繼續
            }
        }

        // 3. 添加子主題關聯
        if (subtopicIds && subtopicIds.length > 0) {
            const subtopicRelations = subtopicIds.map((subtopicId: string) => ({
                thread_id: thread.id,
                subtopic_id: subtopicId,
            }))

            const { error: subtopicError } = await supabase
                .from("thread_subtopics")
                .insert(subtopicRelations)

            if (subtopicError) {
                console.error("添加子主題關聯時出錯:", subtopicError)
                // 這裡可以選擇回滾討論串創建或繼續
            }
        }

        return NextResponse.json({
            success: true,
            data: thread,
            message: "討論串創建成功"
        })

    } catch (error) {
        console.error("處理創建討論串請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "10")
        const topicId = searchParams.get("topicId")
        const status = searchParams.get("status") || "published"

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 計算 offset
        const offset = (page - 1) * limit

        let query = supabase
            .from("threads")
            .select(`
        *,
        profiles:author_id (id, name, avatar),
        thread_topics (topics (*)),
        thread_subtopics (subtopics (*))
      `, { count: "exact" })

        // 根據狀態篩選
        query = query.eq("status", status)

        // 根據主題篩選
        if (topicId) {
            query = query.filter("thread_topics.topic_id", "eq", topicId)
        }

        const { data, error, count } = await query
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("獲取討論串時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取討論串失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("處理獲取討論串請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function PUT(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const body = await request.json()
        const {
            id,
            title,
            content,
            semanticType,
            topicIds,
            subtopicIds,
            status,
        } = body

        // 驗證必要參數
        if (!id || !title || !content || !semanticType) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：ID、標題、內容和語義類型"
            }, { status: 400 })
        }

        // 檢查討論串是否存在且屬於當前用戶
        const { data: existingThread, error: checkError } = await supabase
            .from("threads")
            .select("id, author_id, status")
            .eq("id", id)
            .eq("author_id", user.id)
            .single()

        if (checkError || !existingThread) {
            return NextResponse.json({
                success: false,
                error: "討論串不存在或無權限編輯"
            }, { status: 404 })
        }

        // 更新討論串
        const updateData: any = {
            title,
            content,
            semantic_type: semanticType,
            updated_at: new Date().toISOString(),
        }

        // 如果狀態有變化，更新狀態和發布時間
        if (status && status !== existingThread.status) {
            updateData.status = status
            if (status === "published") {
                updateData.published_at = new Date().toISOString()
            }
        }

        const { data: updatedThread, error: updateError } = await supabase
            .from("threads")
            .update(updateData)
            .eq("id", id)
            .select()
            .single()

        if (updateError) {
            console.error("更新討論串時出錯:", updateError)
            return NextResponse.json({
                success: false,
                error: "更新討論串失敗"
            }, { status: 500 })
        }

        // 更新主題關聯
        if (topicIds !== undefined) {
            // 先刪除現有關聯
            await supabase
                .from("thread_topics")
                .delete()
                .eq("thread_id", id)

            // 添加新關聯
            if (topicIds.length > 0) {
                const topicRelations = topicIds.map((topicId: string) => ({
                    thread_id: id,
                    topic_id: topicId,
                }))

                const { error: topicError } = await supabase
                    .from("thread_topics")
                    .insert(topicRelations)

                if (topicError) {
                    console.error("更新主題關聯時出錯:", topicError)
                }
            }
        }

        // 更新子主題關聯
        if (subtopicIds !== undefined) {
            // 先刪除現有關聯
            await supabase
                .from("thread_subtopics")
                .delete()
                .eq("thread_id", id)

            // 添加新關聯
            if (subtopicIds.length > 0) {
                const subtopicRelations = subtopicIds.map((subtopicId: string) => ({
                    thread_id: id,
                    subtopic_id: subtopicId,
                }))

                const { error: subtopicError } = await supabase
                    .from("thread_subtopics")
                    .insert(subtopicRelations)

                if (subtopicError) {
                    console.error("更新子主題關聯時出錯:", subtopicError)
                }
            }
        }

        return NextResponse.json({
            success: true,
            data: updatedThread,
            message: "討論串更新成功"
        })

    } catch (error) {
        console.error("處理更新討論串請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 