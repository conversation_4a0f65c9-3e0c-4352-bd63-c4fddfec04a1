import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => {
            const resolvedCookies = await cookieStore
            return resolvedCookies.getAll()
          },
          setAll: async (cookiesToSet) => {
            const resolvedCookies = await cookieStore
            cookiesToSet.forEach(({ name, value, options }) => {
              resolvedCookies.set(name, value, options)
            })
          },
        },
      }
    )
    const { commentIds } = await request.json()

    if (!commentIds || !Array.isArray(commentIds) || commentIds.length === 0) {
      return NextResponse.json({ success: false, error: "Invalid comment IDs" }, { status: 400 })
    }

    // 獲取當前用戶
    const {
      data: { user },
    } = await supabase.auth.getUser()

    // 獲取所有評論的反應計數
    const { data: reactionCounts, error: countError } = await supabase
      .from("reactions")
      .select("item_id, reaction_type")
      .in("item_id", commentIds)
      .eq("item_type", "comment")

    if (countError) {
      console.error("Error fetching reaction counts:", countError)
      return NextResponse.json({ success: false, error: "Failed to fetch reaction counts" }, { status: 500 })
    }

    // 統計每個評論的反應數
    const counts: Record<string, { likes: number; dislikes: number }> = {}
    commentIds.forEach((id) => {
      counts[id] = { likes: 0, dislikes: 0 }
    })

    reactionCounts?.forEach((reaction) => {
      if (!counts[reaction.item_id]) {
        counts[reaction.item_id] = { likes: 0, dislikes: 0 }
      }
      if (reaction.reaction_type === "like") {
        counts[reaction.item_id].likes++
      } else if (reaction.reaction_type === "dislike") {
        counts[reaction.item_id].dislikes++
      }
    })

    // 如果用戶已登入，獲取用戶的反應狀態
    const userReactions: Record<string, { liked: boolean; disliked: boolean }> = {}
    if (user) {
      const { data: userReactionData, error: userError } = await supabase
        .from("reactions")
        .select("item_id, reaction_type")
        .eq("profile_id", user.id)
        .in("item_id", commentIds)
        .eq("item_type", "comment")

      if (!userError && userReactionData) {
        commentIds.forEach((id) => {
          userReactions[id] = { liked: false, disliked: false }
        })

        userReactionData.forEach((reaction) => {
          if (!userReactions[reaction.item_id]) {
            userReactions[reaction.item_id] = { liked: false, disliked: false }
          }
          if (reaction.reaction_type === "like") {
            userReactions[reaction.item_id].liked = true
          } else if (reaction.reaction_type === "dislike") {
            userReactions[reaction.item_id].disliked = true
          }
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        counts,
        userReactions: user ? userReactions : {},
      },
    })
  } catch (error) {
    console.error("Error in batch reactions API:", error)
    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
