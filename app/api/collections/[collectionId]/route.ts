import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(
    request: Request,
    { params }: { params: Promise<{ collectionId: string }> }
) {
    try {
        const { collectionId } = await params
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "20")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 首先獲取收藏牆信息
        const { data: collection, error: collectionError } = await supabase
            .from("collections")
            .select("*")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (collectionError || !collection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        // 獲取收藏牆項目
        const { data: collectionItems, error: itemsError, count } = await supabase
            .from("collection_items")
            .select(`
                *,
                cards:card_id (
                    id, title, content, semantic_type, contribution_type,
                    original_author, original_url, created_at, published_at,
                    profiles:author_id (id, name, avatar),
                    card_topics:card_topics (topics:topic_id (name)),
                    card_subtopics:card_subtopics (subtopics:subtopic_id (name))
                ),
                threads:thread_id (
                    id, title, content, semantic_type,
                    created_at, published_at,
                    profiles:author_id (id, name, avatar),
                    thread_topics:thread_topics (topics:topic_id (name)),
                    thread_subtopics:thread_subtopics (subtopics:subtopic_id (name))
                )
            `, { count: "exact" })
            .eq("collection_id", collectionId)
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (itemsError) {
            console.error("獲取收藏牆項目時出錯:", itemsError)
            return NextResponse.json({
                success: false,
                error: "獲取收藏牆項目失敗"
            }, { status: 500 })
        }

        // 處理項目數據，統一格式
        const processedItems = await Promise.all(
            (collectionItems || []).map(async (item) => {
                const isCard = !!item.cards
                const content = isCard ? item.cards : item.threads

                if (!content) return null

                // 獲取反應統計
                const { data: reactions } = await supabase
                    .from("reactions")
                    .select("reaction_type")
                    .eq("item_type", isCard ? "card" : "thread")
                    .eq("item_id", content.id)

                // 獲取評論統計
                const { data: comments } = await supabase
                    .from("comments")
                    .select("id")
                    .eq("root_item_type", isCard ? "card" : "thread")
                    .eq("root_item_id", content.id)

                // 統計反應數據
                const likes = reactions?.filter(r => r.reaction_type === "like").length || 0
                const dislikes = reactions?.filter(r => r.reaction_type === "dislike").length || 0
                const commentCount = comments?.length || 0

                return {
                    id: content.id,
                    contentType: isCard ? "viewpoint" : "discussion",
                    semanticType: content.semantic_type,
                    title: content.title,
                    content: content.content,
                    author: {
                        id: content.profiles?.id || "unknown",
                        name: content.profiles?.name || "未知用戶",
                        avatar: content.profiles?.avatar,
                    },
                    timestamp: new Date(content.created_at).toLocaleDateString("zh-TW"),
                    topics: isCard
                        ? content.card_topics?.map((ct: any) => ct.topics?.name).filter(Boolean) || []
                        : content.thread_topics?.map((tt: any) => tt.topics?.name).filter(Boolean) || [],
                    subtopics: isCard
                        ? content.card_subtopics?.map((cs: any) => cs.subtopics?.name).filter(Boolean) || []
                        : undefined,
                    tags: !isCard
                        ? content.thread_subtopics?.map((ts: any) => ts.subtopics?.name).filter(Boolean) || []
                        : undefined,
                    stats: {
                        likes,
                        dislikes: isCard ? dislikes : undefined,
                        comments: isCard ? commentCount : undefined,
                        replies: !isCard ? commentCount : undefined,
                    },
                    // 觀點卡特有屬性
                    ...(isCard && {
                        contribution_type: content.contribution_type,
                        originalAuthor: content.original_author,
                        originalSource: content.original_url,
                    }),
                    addedAt: item.created_at,
                }
            })
        )

        // 過濾掉空項目
        const validItems = processedItems.filter(item => item !== null)

        // 格式化收藏牆信息
        const formattedCollection = {
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: count || 0,
            isPublic: collection.is_public,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: {
                collection: formattedCollection,
                items: validItems,
                pagination: {
                    total: count || 0,
                    page,
                    limit,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            }
        })

    } catch (error) {
        console.error("處理獲取收藏牆詳情請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function PUT(
    request: Request,
    { params }: { params: Promise<{ collectionId: string }> }
) {
    try {
        const { collectionId } = await params
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { name, description, coverImage, isPublic, categoryId } = await request.json()

        // 檢查收藏牆是否存在且屬於當前用戶
        const { data: existingCollection, error: checkError } = await supabase
            .from("collections")
            .select("id, user_id")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (checkError || !existingCollection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 如果指定了 categoryId，驗證分類是否存在且屬於當前用戶
        if (categoryId && categoryId !== null) {
            const { data: category, error: categoryError } = await supabase
                .from("collection_categories")
                .select("id")
                .eq("id", categoryId)
                .eq("user_id", user.id)
                .single()

            if (categoryError || !category) {
                return NextResponse.json({
                    success: false,
                    error: "指定的分類不存在"
                }, { status: 400 })
            }
        }

        // 構建更新數據
        const updateData: any = {
            updated_at: new Date().toISOString()
        }

        if (name !== undefined) updateData.name = name
        if (description !== undefined) updateData.description = description
        if (coverImage !== undefined) updateData.cover_image = coverImage
        if (isPublic !== undefined) updateData.is_public = isPublic
        if (categoryId !== undefined) updateData.category_id = categoryId

        // 更新收藏牆
        const { data: updatedCollection, error: updateError } = await supabase
            .from("collections")
            .update(updateData)
            .eq("id", collectionId)
            .select()
            .single()

        if (updateError) {
            console.error("更新收藏牆時出錯:", updateError)
            return NextResponse.json({
                success: false,
                error: "更新收藏牆失敗"
            }, { status: 500 })
        }

        // 獲取包含分類資訊的收藏牆資料
        const { data: collectionWithCategory } = await supabase
            .from("collections_with_category")
            .select("*")
            .eq("id", collectionId)
            .single()

        // 格式化返回數據
        const formattedCollection = {
            id: updatedCollection.id,
            name: updatedCollection.name,
            description: updatedCollection.description,
            coverImage: updatedCollection.cover_image,
            itemCount: collectionWithCategory?.item_count || 0,
            isPublic: updatedCollection.is_public,
            categoryId: updatedCollection.category_id,
            categoryName: collectionWithCategory?.category_name || null,
            createdAt: updatedCollection.created_at,
            updatedAt: updatedCollection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCollection,
            message: "收藏牆更新成功"
        })

    } catch (error) {
        console.error("處理更新收藏牆請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}