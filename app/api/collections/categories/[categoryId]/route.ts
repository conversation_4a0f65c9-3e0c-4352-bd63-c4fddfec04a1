import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// GET - 獲取單一分類詳情
export async function GET(
    request: Request,
    { params }: { params: { categoryId: string } }
) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { categoryId } = params

        // 獲取分類詳情，同時統計收藏牆數量
        const { data: category, error } = await supabase
            .from("collection_categories")
            .select(`
                *,
                collections(count)
            `)
            .eq("id", categoryId)
            .eq("user_id", user.id)
            .single()

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({
                    success: false,
                    error: "分類不存在"
                }, { status: 404 })
            }
            console.error("獲取分類詳情時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取分類詳情失敗"
            }, { status: 500 })
        }

        // 格式化數據
        const formattedCategory = {
            id: category.id,
            name: category.name,
            description: category.description,
            sortOrder: category.sort_order,
            count: category.collections?.[0]?.count || 0,
            createdAt: category.created_at,
            updatedAt: category.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCategory
        })

    } catch (error) {
        console.error("處理獲取分類詳情請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

// PUT - 更新分類
export async function PUT(
    request: Request,
    { params }: { params: { categoryId: string } }
) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { categoryId } = params

        // 解析請求體
        const { name, description, sortOrder } = await request.json()

        // 驗證必要參數
        if (!name || !name.trim()) {
            return NextResponse.json({ success: false, error: "分類名稱不能為空" }, { status: 400 })
        }

        // 檢查分類是否存在且屬於當前用戶
        const { data: existingCategory, error: fetchError } = await supabase
            .from("collection_categories")
            .select("*")
            .eq("id", categoryId)
            .eq("user_id", user.id)
            .single()

        if (fetchError) {
            if (fetchError.code === 'PGRST116') {
                return NextResponse.json({
                    success: false,
                    error: "分類不存在"
                }, { status: 404 })
            }
            console.error("獲取分類時出錯:", fetchError)
            return NextResponse.json({
                success: false,
                error: "獲取分類失敗"
            }, { status: 500 })
        }

        // 檢查新名稱是否與其他分類重複（排除自己）
        if (name.trim() !== existingCategory.name) {
            const { data: duplicateCategory } = await supabase
                .from("collection_categories")
                .select("id")
                .eq("user_id", user.id)
                .eq("name", name.trim())
                .neq("id", categoryId)
                .single()

            if (duplicateCategory) {
                return NextResponse.json({
                    success: false,
                    error: "分類名稱已存在"
                }, { status: 400 })
            }
        }

        // 構建更新數據
        const updateData: any = {
            name: name.trim(),
            description: description?.trim() || "",
        }

        // 如果提供了 sortOrder，則更新排序
        if (typeof sortOrder === 'number') {
            updateData.sort_order = sortOrder
        }

        // 更新分類
        const { data: updatedCategory, error: updateError } = await supabase
            .from("collection_categories")
            .update(updateData)
            .eq("id", categoryId)
            .eq("user_id", user.id)
            .select()
            .single()

        if (updateError) {
            console.error("更新分類時出錯:", updateError)
            return NextResponse.json({
                success: false,
                error: "更新分類失敗"
            }, { status: 500 })
        }

        // 格式化返回數據
        const formattedCategory = {
            id: updatedCategory.id,
            name: updatedCategory.name,
            description: updatedCategory.description,
            sortOrder: updatedCategory.sort_order,
            createdAt: updatedCategory.created_at,
            updatedAt: updatedCategory.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCategory,
            message: "分類更新成功"
        })

    } catch (error) {
        console.error("處理更新分類請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

// DELETE - 刪除分類
export async function DELETE(
    request: Request,
    { params }: { params: { categoryId: string } }
) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { categoryId } = params

        // 檢查分類是否存在且屬於當前用戶
        const { data: existingCategory, error: fetchError } = await supabase
            .from("collection_categories")
            .select("*")
            .eq("id", categoryId)
            .eq("user_id", user.id)
            .single()

        if (fetchError) {
            if (fetchError.code === 'PGRST116') {
                return NextResponse.json({
                    success: false,
                    error: "分類不存在"
                }, { status: 404 })
            }
            console.error("獲取分類時出錯:", fetchError)
            return NextResponse.json({
                success: false,
                error: "獲取分類失敗"
            }, { status: 500 })
        }

        // 檢查是否有收藏牆正在使用此分類
        const { data: collectionsInCategory, error: collectionsError } = await supabase
            .from("collections")
            .select("id")
            .eq("category_id", categoryId)
            .limit(1)

        if (collectionsError) {
            console.error("檢查分類使用狀況時出錯:", collectionsError)
            return NextResponse.json({
                success: false,
                error: "檢查分類使用狀況失敗"
            }, { status: 500 })
        }

        if (collectionsInCategory && collectionsInCategory.length > 0) {
            return NextResponse.json({
                success: false,
                error: "無法刪除含有收藏牆的分類，請先移動或刪除分類內的收藏牆"
            }, { status: 400 })
        }

        // 刪除分類
        const { error: deleteError } = await supabase
            .from("collection_categories")
            .delete()
            .eq("id", categoryId)
            .eq("user_id", user.id)

        if (deleteError) {
            console.error("刪除分類時出錯:", deleteError)
            return NextResponse.json({
                success: false,
                error: "刪除分類失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            message: "分類刪除成功"
        })

    } catch (error) {
        console.error("處理刪除分類請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 