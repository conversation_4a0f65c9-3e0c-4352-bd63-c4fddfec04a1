import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"

// GET - 獲取用戶的分類列表
export async function GET(request: Request) {
    try {
        const supabase = createServerClient()

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 獲取用戶的分類列表
        const { data: categories, error } = await supabase
            .from("collection_categories")
            .select("*")
            .eq("user_id", user.id)
            .order("sort_order", { ascending: true })

        if (error) {
            console.error("獲取分類列表時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取分類列表失敗"
            }, { status: 500 })
        }

        // 如果沒有分類，直接返回空陣列
        if (!categories || categories.length === 0) {
            return NextResponse.json({
                success: true,
                data: []
            })
        }

        // 為每個分類統計收藏牆數量
        const processedCategories = await Promise.all(
            categories.map(async (category) => {
                // 統計該分類下的收藏牆數量
                const { count } = await supabase
                    .from("collections")
                    .select("*", { count: "exact", head: true })
                    .eq("user_id", user.id)
                    .eq("category_id", category.id)

                return {
                    id: category.id,
                    name: category.name,
                    description: category.description,
                    sortOrder: category.sort_order,
                    count: count || 0,
                    createdAt: category.created_at,
                    updatedAt: category.updated_at,
                }
            })
        )

        return NextResponse.json({
            success: true,
            data: processedCategories
        })

    } catch (error) {
        console.error("處理獲取分類列表請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

// POST - 創建新分類
export async function POST(request: Request) {
    try {
        const supabase = createServerClient()

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { name, description } = await request.json()

        // 驗證必要參數
        if (!name || !name.trim()) {
            return NextResponse.json({ success: false, error: "分類名稱不能為空" }, { status: 400 })
        }

        // 檢查分類名稱是否已存在
        const { data: existingCategory } = await supabase
            .from("collection_categories")
            .select("id")
            .eq("user_id", user.id)
            .eq("name", name.trim())
            .single()

        if (existingCategory) {
            return NextResponse.json({
                success: false,
                error: "分類名稱已存在"
            }, { status: 400 })
        }

        // 獲取當前用戶的最大排序值
        const { data: maxOrderResult } = await supabase
            .from("collection_categories")
            .select("sort_order")
            .eq("user_id", user.id)
            .order("sort_order", { ascending: false })
            .limit(1)
            .single()

        const nextSortOrder = (maxOrderResult?.sort_order || 0) + 1

        // 創建新分類
        const { data: category, error: insertError } = await supabase
            .from("collection_categories")
            .insert({
                user_id: user.id,
                name: name.trim(),
                description: description?.trim() || "",
                sort_order: nextSortOrder,
            })
            .select()
            .single()

        if (insertError) {
            console.error("創建分類時出錯:", insertError)
            return NextResponse.json({
                success: false,
                error: "創建分類失敗"
            }, { status: 500 })
        }

        // 格式化返回數據
        const formattedCategory = {
            id: category.id,
            name: category.name,
            description: category.description,
            sortOrder: category.sort_order,
            count: 0, // 新創建的分類沒有收藏牆
            createdAt: category.created_at,
            updatedAt: category.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCategory,
            message: "分類創建成功"
        })

    } catch (error) {
        console.error("處理創建分類請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 