import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "50")
        const categoryId = searchParams.get("categoryId") // 可選的分類篩選

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        // 使用視圖獲取收藏牆列表，包含分類資訊
        let query = supabase
            .from("collections_with_category")
            .select("*", { count: "exact" })
            .eq("user_id", user.id)

        // 如果指定了分類，則過濾
        if (categoryId) {
            if (categoryId === "uncategorized") {
                query = query.is("category_id", null)
            } else {
                query = query.eq("category_id", categoryId)
            }
        }

        const { data: collections, error, count } = await query
            .order("updated_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("獲取收藏牆列表時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取收藏牆列表失敗"
            }, { status: 500 })
        }

        // 處理數據
        const processedCollections = collections?.map(collection => ({
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: collection.item_count || 0,
            isPublic: collection.is_public,
            categoryId: collection.category_id,
            categoryName: collection.category_name,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        })) || []

        return NextResponse.json({
            success: true,
            data: processedCollections,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("處理獲取收藏牆列表請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { name, description, coverImage, isPublic = true, categoryId } = await request.json()

        // 驗證必要參數
        if (!name) {
            return NextResponse.json({ success: false, error: "缺少收藏牆名稱" }, { status: 400 })
        }

        // 如果指定了 categoryId，驗證分類是否存在且屬於當前用戶
        if (categoryId) {
            const { data: category, error: categoryError } = await supabase
                .from("collection_categories")
                .select("id")
                .eq("id", categoryId)
                .eq("user_id", user.id)
                .single()

            if (categoryError || !category) {
                return NextResponse.json({
                    success: false,
                    error: "指定的分類不存在"
                }, { status: 400 })
            }
        }

        // 創建收藏牆
        const { data: collection, error: insertError } = await supabase
            .from("collections")
            .insert({
                user_id: user.id,
                name,
                description: description || "",
                cover_image: coverImage || "/abstract-geometric-shapes.png",
                is_public: isPublic,
                category_id: categoryId || null,
            })
            .select()
            .single()

        if (insertError) {
            console.error("創建收藏牆時出錯:", insertError)
            return NextResponse.json({
                success: false,
                error: "創建收藏牆失敗"
            }, { status: 500 })
        }

        // 獲取包含分類資訊的收藏牆資料
        const { data: collectionWithCategory } = await supabase
            .from("collections_with_category")
            .select("*")
            .eq("id", collection.id)
            .single()

        // 格式化返回數據
        const formattedCollection = {
            id: collection.id,
            name: collection.name,
            description: collection.description,
            coverImage: collection.cover_image,
            itemCount: 0, // 新創建的收藏牆沒有項目
            isPublic: collection.is_public,
            categoryId: collection.category_id,
            categoryName: collectionWithCategory?.category_name || null,
            createdAt: collection.created_at,
            updatedAt: collection.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedCollection,
            message: "收藏牆創建成功"
        })

    } catch (error) {
        console.error("處理創建收藏牆請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 