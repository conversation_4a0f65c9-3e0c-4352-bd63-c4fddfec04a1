import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"

export async function GET(request: NextRequest) {
    try {
        const supabase = createServerClient()

        // 取得用戶資料
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError || !user) {
            return NextResponse.json(
                { success: false, error: "未授權訪問" },
                { status: 401 }
            )
        }

        // 獲取用戶收藏的 cards 和 threads ID
        const { data: bookmarks, error: bookmarksError } = await supabase
            .from("bookmarks")
            .select("item_id, item_type")
            .eq("profile_id", user.id)

        if (bookmarksError) {
            console.error("獲取收藏列表時出錯:", bookmarksError)
            return NextResponse.json(
                { success: false, error: "獲取收藏列表失敗" },
                { status: 500 }
            )
        }

        if (!bookmarks || bookmarks.length === 0) {
            return NextResponse.json({
                success: true,
                data: [],
                total: 0,
            })
        }

        // 分離 cards 和 threads ID
        const cardIds = bookmarks.filter(b => b.item_type === 'card').map(b => b.item_id)
        const threadIds = bookmarks.filter(b => b.item_type === 'thread').map(b => b.item_id)

        // 計算每個主題/子主題的使用次數
        const topicCounts = new Map<string, { name: string, count: number, type: string }>()

        // 獲取收藏卡片的主題
        if (cardIds.length > 0) {
            const { data: cardTopics } = await supabase
                .from("card_topics")
                .select(`
                    topic_id,
                    topics(id, name)
                `)
                .in("card_id", cardIds)

            if (cardTopics) {
                cardTopics.forEach((item: any) => {
                    const topic = item.topics
                    if (topic) {
                        const key = `topic-${topic.id}`
                        if (topicCounts.has(key)) {
                            topicCounts.get(key)!.count += 1
                        } else {
                            topicCounts.set(key, {
                                name: topic.name,
                                count: 1,
                                type: "topic"
                            })
                        }
                    }
                })
            }

            // 獲取收藏卡片的子主題
            const { data: cardSubtopics } = await supabase
                .from("card_subtopics")
                .select(`
                    subtopic_id,
                    subtopics(id, name)
                `)
                .in("card_id", cardIds)

            if (cardSubtopics) {
                cardSubtopics.forEach((item: any) => {
                    const subtopic = item.subtopics
                    if (subtopic) {
                        const key = `subtopic-${subtopic.id}`
                        if (topicCounts.has(key)) {
                            topicCounts.get(key)!.count += 1
                        } else {
                            topicCounts.set(key, {
                                name: subtopic.name,
                                count: 1,
                                type: "subtopic"
                            })
                        }
                    }
                })
            }
        }

        // 獲取收藏討論串的主題
        if (threadIds.length > 0) {
            const { data: threadTopics } = await supabase
                .from("thread_topics")
                .select(`
                    topic_id,
                    topics(id, name)
                `)
                .in("thread_id", threadIds)

            if (threadTopics) {
                threadTopics.forEach((item: any) => {
                    const topic = item.topics
                    if (topic) {
                        const key = `topic-${topic.id}`
                        if (topicCounts.has(key)) {
                            topicCounts.get(key)!.count += 1
                        } else {
                            topicCounts.set(key, {
                                name: topic.name,
                                count: 1,
                                type: "topic"
                            })
                        }
                    }
                })
            }

            // 獲取收藏討論串的子主題
            const { data: threadSubtopics } = await supabase
                .from("thread_subtopics")
                .select(`
                    subtopic_id,
                    subtopics(id, name)
                `)
                .in("thread_id", threadIds)

            if (threadSubtopics) {
                threadSubtopics.forEach((item: any) => {
                    const subtopic = item.subtopics
                    if (subtopic) {
                        const key = `subtopic-${subtopic.id}`
                        if (topicCounts.has(key)) {
                            topicCounts.get(key)!.count += 1
                        } else {
                            topicCounts.set(key, {
                                name: subtopic.name,
                                count: 1,
                                type: "subtopic"
                            })
                        }
                    }
                })
            }
        }

        // 轉換為所需格式並排序
        const tags = Array.from(topicCounts.entries()).map(([id, data]) => ({
            id,
            name: data.name,
            count: data.count,
            type: data.type as "topic" | "subtopic",
            originalId: id.split('-')[1],
        }))

        // 按使用次數排序
        const sortedTags = tags
            .sort((a, b) => b.count - a.count)
            .slice(0, 20) // 限制最多顯示 20 個標籤

        return NextResponse.json({
            success: true,
            data: sortedTags,
            total: sortedTags.length,
        })

    } catch (error) {
        console.error("獲取標籤時出錯:", error)
        return NextResponse.json(
            { success: false, error: "伺服器錯誤" },
            { status: 500 }
        )
    }
} 