import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
                cookies: {
                    getAll: async () => {
                        const resolvedCookies = await cookieStore
                        return resolvedCookies.getAll()
                    },
                    setAll: async (cookiesToSet) => {
                        const resolvedCookies = await cookieStore
                        cookiesToSet.forEach(({ name, value, options }) => {
                            resolvedCookies.set(name, value, options)
                        })
                    },
                },
            }
        )

        const { data: { user }, error } = await supabase.auth.getUser()

        if (error) {
            console.error('User check error:', error)
            return NextResponse.json({
                isAuthenticated: false,
                error: error.message
            }, { status: 200 })
        }

        return NextResponse.json({
            isAuthenticated: !!user,
            user: user ? {
                id: user.id,
                email: user.email,
            } : null,
            session: !!user,
        })
    } catch (error) {
        console.error('Session API error:', error)
        return NextResponse.json({
            isAuthenticated: false,
            error: 'Internal server error'
        }, { status: 500 })
    }
} 