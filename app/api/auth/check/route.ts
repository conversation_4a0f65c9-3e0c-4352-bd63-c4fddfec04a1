import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      console.error("Error checking auth status:", error)
      return NextResponse.json(
        {
          authenticated: false,
          error: error.message,
        },
        { status: 401 },
      )
    }

    return NextResponse.json({
      authenticated: !!user,
      user: user ? {
        id: user.id,
        email: user.email
      } : null,
    })
  } catch (error) {
    console.error("Unexpected error in auth check:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: "Internal server error",
      },
      { status: 500 },
    )
  }
}
