import { NextResponse } from "next/server"
import { getPopularSubtopics } from "@/lib/topic-service"

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const limit = url.searchParams.get("limit") ? Number.parseInt(url.searchParams.get("limit") as string) : 10

    const response = await getPopularSubtopics(limit)

    // Always return a proper JSON response
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching popular subtopics:", error)
    // Ensure we return a valid JSON response even for errors
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch popular subtopics",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
