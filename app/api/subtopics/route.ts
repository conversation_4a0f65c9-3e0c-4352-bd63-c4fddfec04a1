import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)
        const searchParams = request.nextUrl.searchParams

        const topicId = searchParams.get("topicId")
        const limit = Number.parseInt(searchParams.get("limit") || "100", 10)

        let query = supabase
            .from("subtopics")
            .select(`
        id,
        name,
        slug,
        description,
        topic_id,
        topics:topic_id(id, name, slug)
      `)
            .order("name")
            .limit(limit)

        // 如果指定了 topicId，只獲取該主題下的子主題
        if (topicId) {
            query = query.eq("topic_id", topicId)
        }

        const { data: subtopics, error } = await query

        if (error) {
            console.error("Error fetching subtopics:", error)
            return NextResponse.json(
                { success: false, error: "Failed to fetch subtopics" },
                { status: 500 }
            )
        }

        return NextResponse.json({
            success: true,
            data: subtopics || [],
        })
    } catch (error) {
        console.error("Subtopics API error:", error)
        return NextResponse.json(
            { success: false, error: "Internal server error" },
            { status: 500 }
        )
    }
} 