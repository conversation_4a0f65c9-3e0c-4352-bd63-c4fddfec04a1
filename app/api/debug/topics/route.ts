import { NextResponse } from "next/server"
import { getSupabase } from "@/lib/api-utils"

export async function GET() {
  try {
    const supabase = getSupabase()

    // 獲取所有主題
    const { data: topics, error } = await supabase.from("topics").select("*")

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      count: topics?.length || 0,
      topics: topics || [],
    })
  } catch (error) {
    console.error("Error in debug topics API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
