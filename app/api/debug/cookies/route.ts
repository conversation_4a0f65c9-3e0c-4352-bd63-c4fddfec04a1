import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
    try {
        const cookieStore = await cookies()
        const allCookies = cookieStore.getAll()

        const authCookies = allCookies.filter(cookie =>
            cookie.name.includes('sb-') || cookie.name.includes('supabase')
        )

        return NextResponse.json({
            totalCookies: allCookies.length,
            authCookies: authCookies.map(c => ({
                name: c.name,
                valuePreview: c.value.slice(0, 50) + '...',
                hasValue: !!c.value
            })),
            allCookieNames: allCookies.map(c => c.name),
            requestCookies: request.cookies.getAll().map(c => ({
                name: c.name,
                valuePreview: c.value.slice(0, 50) + '...'
            }))
        })
    } catch (error) {
        console.error('Cookie debug error:', error)
        return NextResponse.json({
            error: '<PERSON><PERSON> debug failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
} 