import { NextResponse } from "next/server"
import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => {
            const resolvedCookies = await cookieStore
            return resolvedCookies.getAll()
          },
          setAll: async (cookiesToSet) => {
            const resolvedCookies = await cookieStore
            cookiesToSet.forEach(({ name, value, options }) => {
              resolvedCookies.set(name, value, options)
            })
          },
        },
      }
    )

    // 檢查用戶是否已登入
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser()

    if (userError) {
      return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 解析請求體
    let { itemType, itemId, reactionType } = await request.json()

    // 確保 itemId 是字符串
    if (typeof itemId === "number") {
      itemId = itemId.toString()
    }

    // 檢查是否為有效的 UUID 格式
    if (!isValidUUID(itemId)) {
      console.log("itemId 不是有效的 UUID 格式:", itemId)
      return NextResponse.json({
        success: false,
        error: "無效的項目 ID 格式，僅支援 UUID"
      }, { status: 400 })
    }

    // 驗證必要參數
    if (!itemType || !itemId || !reactionType) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查用戶是否已經對該項目做出了相同類型的反應
    const { data: existingReaction, error: checkError } = await supabase
      .from("reactions")
      .select("id")
      .eq("profile_id", user.id)
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .maybeSingle()

    if (checkError) {
      console.error("檢查反應時出錯:", checkError)
      return NextResponse.json({ success: false, error: "檢查反應時出錯" }, { status: 500 })
    }

    // 如果已經有反應，則刪除它（取消反應）
    if (existingReaction) {
      const { error: deleteError } = await supabase.from("reactions").delete().eq("id", existingReaction.id)

      if (deleteError) {
        console.error("刪除反應時出錯:", deleteError)
        return NextResponse.json({ success: false, error: "刪除反應時出錯" }, { status: 500 })
      }

      return NextResponse.json({ success: true, data: { action: "removed" } })
    }

    // 如果是互斥反應（如點讚和倒讚），則先刪除互斥的反應
    if (reactionType === "like" || reactionType === "dislike") {
      const oppositeType = reactionType === "like" ? "dislike" : "like"

      await supabase
        .from("reactions")
        .delete()
        .eq("profile_id", user.id)
        .eq("item_type", itemType)
        .eq("item_id", itemId)
        .eq("reaction_type", oppositeType)
    }

    // 添加新的反應
    const { error: insertError } = await supabase.from("reactions").insert({
      profile_id: user.id,
      item_type: itemType,
      item_id: itemId,
      reaction_type: reactionType,
    })

    if (insertError) {
      console.error("添加反應時出錯:", insertError)
      return NextResponse.json({ success: false, error: "添加反應時出錯" }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: { action: "added" } })
  } catch (error) {
    console.error("處理反應時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
