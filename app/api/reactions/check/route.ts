import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const itemType = searchParams.get("itemType")
    const itemId = searchParams.get("itemId")
    const reactionType = searchParams.get("reactionType")

    console.log("Checking reaction:", { itemType, itemId, reactionType })

    if (!itemType || !itemId || !reactionType) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查 itemId 是否為有效的 UUID
    if (!isValidUUID(itemId)) {
      console.log(`Invalid UUID format: ${itemId}, returning false`)
      // 如果不是有效的 UUID，直接返回未反應狀態
      return NextResponse.json({
        success: true,
        data: { hasReacted: false },
        warning: "Invalid UUID format, assuming no reaction"
      })
    }

    const cookieStore = cookies()
    const supabase = await createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => {
            const resolvedCookies = await cookieStore
            return resolvedCookies.getAll()
          },
          setAll: async (cookiesToSet) => {
            const resolvedCookies = await cookieStore
            cookiesToSet.forEach(({ name, value, options }) => {
              resolvedCookies.set(name, value, options)
            })
          },
        },
      }
    )

    // 獲取當前用戶
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser()

    if (userError) {
      console.error("獲取用戶時出錯:", userError)
      return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
    }

    if (!user) {
      console.debug("用戶未登入，返回未反應狀態")
      return NextResponse.json({ success: true, data: { hasReacted: false } })
    }

    const userId = user.id

    // 檢查用戶是否已經對該項目進行了反應
    const { data, error } = await supabase
      .from("reactions")
      .select("id")
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .eq("profile_id", userId)
      .maybeSingle()

    if (error) {
      console.error("檢查反應時出錯:", error)
      return NextResponse.json({ success: false, error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: { hasReacted: !!data } })
  } catch (error) {
    console.error("處理檢查反應時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
