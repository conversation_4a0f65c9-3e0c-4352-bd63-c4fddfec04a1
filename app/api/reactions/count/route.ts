import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const itemType = searchParams.get("itemType")
    const itemId = searchParams.get("itemId")

    console.log("Fetching reaction counts:", { itemType, itemId })

    if (!itemType || !itemId) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查 itemId 是否為有效的 UUID
    if (!isValidUUID(itemId)) {
      console.log(`Invalid UUID format: ${itemId}, returning zero counts`)
      // 如果不是有效的 UUID，返回零計數
      return NextResponse.json({
        success: true,
        data: { like: 0, dislike: 0 },
        warning: "Invalid UUID format, returning zero counts",
      })
    }

    const cookieStore = cookies()
    const supabase = await createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => {
            const resolvedCookies = await cookieStore
            return resolvedCookies.getAll()
          },
          setAll: async (cookiesToSet) => {
            const resolvedCookies = await cookieStore
            cookiesToSet.forEach(({ name, value, options }) => {
              resolvedCookies.set(name, value, options)
            })
          },
        },
      }
    )

    // 獲取反應計數
    const { data, error } = await supabase
      .from("reactions")
      .select("reaction_type")
      .eq("item_type", itemType)
      .eq("item_id", itemId)

    if (error) {
      console.error("獲取反應計數時出錯:", error)
      return NextResponse.json({ success: false, error: error.message }, { status: 500 })
    }

    // 計算每種反應類型的數量
    const counts: Record<string, number> = {}
    data.forEach((reaction) => {
      const type = reaction.reaction_type
      counts[type] = (counts[type] || 0) + 1
    })

    console.log("Reaction counts result:", counts)

    return NextResponse.json({ success: true, data: counts })
  } catch (error) {
    console.error("處理反應計數時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
