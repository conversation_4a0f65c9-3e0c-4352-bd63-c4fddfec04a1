import { NextResponse } from "next/server"
import { getCardStats } from "@/lib/topic-card-service"

export async function GET(request: Request, { params }: { params: Promise<{ cardId: string }> }) {
  try {
    const { cardId } = await params
    const response = await getCardStats(cardId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching card stats:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch card stats" }, { status: 500 })
  }
}
