import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const { searchParams } = new URL(request.url)
        const type = searchParams.get("type") // "card" | "thread"
        const id = searchParams.get("id")

        if (!type || !id) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：type 和 id"
            }, { status: 400 })
        }

        const tableName = type === "card" ? "cards" : "threads"

        // 獲取草稿內容
        const { data: draft, error: fetchError } = await supabase
            .from(tableName)
            .select(`
        *,
        ${type === "card" ? `
          card_topics (topics (*)),
          card_subtopics (subtopics (*))
        ` : `
          thread_topics (topics (*)),
          thread_subtopics (subtopics (*))
        `}
      `)
            .eq("id", id)
            .eq("author_id", user.id)
            .eq("status", "draft")
            .single()

        if (fetchError) {
            return NextResponse.json({
                success: false,
                error: "草稿不存在或無權限訪問"
            }, { status: 404 })
        }

        // 格式化返回數據
        const formattedDraft = {
            id: draft.id,
            title: draft.title,
            content: draft.content,
            semanticType: draft.semantic_type,
            contributionType: type === "card" ? draft.contribution_type : undefined,
            originalAuthor: type === "card" ? draft.original_author : undefined,
            originalUrl: type === "card" ? draft.original_url : undefined,
            topicIds: type === "card"
                ? draft.card_topics?.map((ct: any) => ct.topics.id) || []
                : draft.thread_topics?.map((tt: any) => tt.topics.id) || [],
            subtopicIds: type === "card"
                ? draft.card_subtopics?.map((cs: any) => cs.subtopics.id) || []
                : draft.thread_subtopics?.map((ts: any) => ts.subtopics.id) || [],
            tags: type === "card"
                ? draft.card_subtopics?.map((cs: any) => cs.subtopics.name) || []
                : draft.thread_subtopics?.map((ts: any) => ts.subtopics.name) || [],
            status: draft.status,
            createdAt: draft.created_at,
            updatedAt: draft.updated_at,
        }

        return NextResponse.json({
            success: true,
            data: formattedDraft
        })

    } catch (error) {
        console.error("處理獲取草稿請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 