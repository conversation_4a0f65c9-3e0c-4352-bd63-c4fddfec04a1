import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const page = parseInt(searchParams.get("page") || "1")
        const limit = parseInt(searchParams.get("limit") || "10")
        const itemType = searchParams.get("itemType") // "card" | "thread" | "all"

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 計算 offset
        const offset = (page - 1) * limit

        // 先獲取收藏記錄
        let query = supabase
            .from("bookmarks")
            .select("*", { count: "exact" })
            .eq("profile_id", user.id)

        // 根據類型篩選
        if (itemType && itemType !== "all") {
            query = query.eq("item_type", itemType)
        }

        const { data: bookmarks, error, count } = await query
            .order("created_at", { ascending: false })
            .range(offset, offset + limit - 1)

        if (error) {
            console.error("獲取收藏列表時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "獲取收藏列表失敗"
            }, { status: 500 })
        }

        if (!bookmarks || bookmarks.length === 0) {
            return NextResponse.json({
                success: true,
                data: [],
                pagination: {
                    total: count || 0,
                    page,
                    limit,
                    totalPages: Math.ceil((count || 0) / limit)
                }
            })
        }

        // 分別獲取卡片和討論串的詳細信息
        const cardIds = bookmarks.filter(b => b.item_type === "card").map(b => b.item_id)
        const threadIds = bookmarks.filter(b => b.item_type === "thread").map(b => b.item_id)

        let cards: any[] = []
        let threads: any[] = []

        // 獲取卡片詳情
        if (cardIds.length > 0) {
            const { data: cardsData, error: cardsError } = await supabase
                .from("cards")
                .select(`
                    id, title, content, semantic_type, contribution_type,
                    original_author, original_url, created_at, published_at,
                    profiles:author_id (id, name, avatar)
                `)
                .in("id", cardIds)

            if (!cardsError && cardsData) {
                cards = cardsData
            }
        }

        // 獲取討論串詳情
        if (threadIds.length > 0) {
            const { data: threadsData, error: threadsError } = await supabase
                .from("threads")
                .select(`
                    id, title, content, semantic_type,
                    created_at, published_at,
                    profiles:author_id (id, name, avatar)
                `)
                .in("id", threadIds)

            if (!threadsError && threadsData) {
                threads = threadsData
            }
        }

        // 合併數據
        const processedData = bookmarks.map(bookmark => {
            let content = null

            if (bookmark.item_type === "card") {
                content = cards.find(card => card.id === bookmark.item_id)
            } else {
                content = threads.find(thread => thread.id === bookmark.item_id)
            }

            return {
                ...bookmark,
                content: content
            }
        }).filter(item => item.content !== null) // 過濾掉找不到內容的項目

        return NextResponse.json({
            success: true,
            data: processedData,
            pagination: {
                total: count || 0,
                page,
                limit,
                totalPages: Math.ceil((count || 0) / limit)
            }
        })

    } catch (error) {
        console.error("處理獲取收藏列表請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { itemType, itemId } = await request.json()

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 檢查用戶是否已經收藏該項目
        const { data: existingBookmark, error: checkError } = await supabase
            .from("bookmarks")
            .select("id")
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)
            .maybeSingle()

        if (checkError) {
            console.error("檢查收藏時出錯:", checkError)
            return NextResponse.json({ success: false, error: "檢查收藏時出錯" }, { status: 500 })
        }

        // 如果已經收藏，則取消收藏
        if (existingBookmark) {
            const { error: deleteError } = await supabase
                .from("bookmarks")
                .delete()
                .eq("id", existingBookmark.id)

            if (deleteError) {
                console.error("取消收藏時出錯:", deleteError)
                return NextResponse.json({ success: false, error: "取消收藏時出錯" }, { status: 500 })
            }

            return NextResponse.json({
                success: true,
                data: { action: "removed" },
                message: "已取消收藏"
            })
        }

        // 如果沒有收藏，則添加收藏
        const { error: insertError } = await supabase
            .from("bookmarks")
            .insert({
                profile_id: user.id,
                item_type: itemType,
                item_id: itemId,
            })

        if (insertError) {
            console.error("添加收藏時出錯:", insertError)
            return NextResponse.json({ success: false, error: "添加收藏時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: { action: "added" },
            message: "已加入收藏"
        })

    } catch (error) {
        console.error("處理收藏時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
}

export async function DELETE(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        const body = await request.json()
        const { itemType, itemId } = body

        if (!itemType || !itemId) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：項目類型和項目ID"
            }, { status: 400 })
        }

        // 刪除收藏
        const { error } = await supabase
            .from("bookmarks")
            .delete()
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) {
            console.error("刪除收藏時出錯:", error)
            return NextResponse.json({
                success: false,
                error: "刪除收藏失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            message: "取消收藏成功"
        })

    } catch (error) {
        console.error("處理刪除收藏請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
} 