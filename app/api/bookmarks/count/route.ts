import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 計算收藏數量
        const { count, error } = await supabase
            .from("bookmarks")
            .select("*", { count: "exact", head: true })
            .eq("item_type", itemType)
            .eq("item_id", itemId)

        if (error) {
            console.error("計算收藏數量時出錯:", error)
            return NextResponse.json({ success: false, error: "計算收藏數量時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: {
                count: count || 0
            }
        })

    } catch (error) {
        console.error("處理計算收藏數量請求時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
} 