import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url)
        const itemType = searchParams.get("itemType")
        const itemId = searchParams.get("itemId")

        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 驗證必要參數
        if (!itemType || !itemId) {
            return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({ success: false, error: "itemType 必須是 card 或 thread" }, { status: 400 })
        }

        // 檢查是否已收藏
        const { data: bookmark, error: checkError } = await supabase
            .from("bookmarks")
            .select("id")
            .eq("profile_id", user.id)
            .eq("item_type", itemType)
            .eq("item_id", itemId)
            .maybeSingle()

        if (checkError) {
            console.error("檢查收藏狀態時出錯:", checkError)
            return NextResponse.json({ success: false, error: "檢查收藏狀態時出錯" }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: {
                isBookmarked: !!bookmark
            }
        })

    } catch (error) {
        console.error("處理檢查收藏狀態請求時出錯:", error)
        return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
    }
} 