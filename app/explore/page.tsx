"use client"

import type React from "react"
import { useEffect, useState, useCallback } from "react"
import { useSearchParams } from "next/navigation"
import { ContentCard } from "@/components/content-card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Search, X, Loader2, Filter } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"

// 語義類型選項 - 與 content-card.tsx 保持一致
const SEMANTIC_TYPES = [
  // 觀點卡語義類型
  { value: "insight", label: "看法" },
  { value: "experience", label: "實測經驗" },
  { value: "guide", label: "工具教學" },
  { value: "trap", label: "踩坑警示" },
  { value: "debate", label: "爭議論點" },
  { value: "concept", label: "概念整理" },
  // 討論語義類型
  { value: "discussion", label: "討論" },
  { value: "question", label: "問題" },
  { value: "brainstorm", label: "集思廣益" },
  { value: "chat", label: "閒聊" },
]

// 主題和子主題的類型定義
interface Topic {
  id: string
  name: string
  slug: string
  description?: string
  subtopics?: Subtopic[]
}

interface Subtopic {
  id: string
  name: string
  slug: string
  description?: string
  topic_id: string
  topics?: {
    id: string
    name: string
    slug: string
  }
}

export default function ExplorePage() {
  const searchParams = useSearchParams()
  const [type, setType] = useState(searchParams.get("type") || "all")
  const [sort, setSort] = useState(searchParams.get("sort") || "trending")
  const [page, setPage] = useState(1)
  const [limit] = useState(20)
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "")
  const [searchInput, setSearchInput] = useState(searchParams.get("q") || "")
  const [selectedSemanticTypes, setSelectedSemanticTypes] = useState<string[]>([])
  const [selectedTopics, setSelectedTopics] = useState<string[]>([])
  const [selectedSubtopics, setSelectedSubtopics] = useState<string[]>([])
  const [results, setResults] = useState<any[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 新增：從資料庫獲取的主題和子主題
  const [availableTopics, setAvailableTopics] = useState<Topic[]>([])
  const [availableSubtopics, setAvailableSubtopics] = useState<Subtopic[]>([])
  const [isLoadingFilters, setIsLoadingFilters] = useState(true)

  // 載入可用的主題和子主題
  useEffect(() => {
    const loadFilterOptions = async () => {
      setIsLoadingFilters(true)
      try {
        // 並行獲取主題和子主題
        const [topicsResponse, subtopicsResponse] = await Promise.all([
          fetch('/api/topics?limit=50'),
          fetch('/api/subtopics?limit=200')
        ])

        if (topicsResponse.ok) {
          const topicsData = await topicsResponse.json()
          if (topicsData.success) {
            setAvailableTopics(topicsData.data || [])
          }
        }

        if (subtopicsResponse.ok) {
          const subtopicsData = await subtopicsResponse.json()
          if (subtopicsData.success) {
            setAvailableSubtopics(subtopicsData.data || [])
          }
        }
      } catch (error) {
        console.error('Error loading filter options:', error)
      } finally {
        setIsLoadingFilters(false)
      }
    }

    loadFilterOptions()
  }, [])

  // 獲取數據
  const fetchData = async (pageNum = 1, append = false) => {
    if (append) {
      setIsLoadingMore(true)
    } else {
      setIsLoading(true)
      setResults([]) // 清空現有結果
    }
    setError(null)

    try {
      console.log("Fetching explore data with params:", {
        type,
        sort,
        page: pageNum,
        limit,
        semanticTypes: selectedSemanticTypes,
        topics: [...selectedTopics, ...selectedSubtopics], // 合併主題和子主題
        q: searchQuery,
      })

      const queryParams = new URLSearchParams({
        type,
        sort,
        page: pageNum.toString(),
        limit: limit.toString(),
      })

      if (selectedSemanticTypes.length > 0) {
        queryParams.set("semanticTypes", selectedSemanticTypes.join(","))
      }

      // 合併主題和子主題過濾
      const allTopicFilters = [...selectedTopics, ...selectedSubtopics]
      if (allTopicFilters.length > 0) {
        queryParams.set("topics", allTopicFilters.join(","))
      }

      if (searchQuery) {
        queryParams.set("q", searchQuery)
      }

      const requestUrl = `/api/explore?${queryParams.toString()}`
      console.log("Request URL:", requestUrl)

      const response = await fetch(requestUrl)

      if (!response.ok) {
        // 嘗試獲取詳細的錯誤信息
        let errorDetails = `API error: ${response.status} ${response.statusText}`
        try {
          const errorBody = await response.text()
          console.error("Error response body:", errorBody)
          errorDetails += ` - ${errorBody}`
        } catch (e) {
          console.error("Could not read error response body:", e)
        }
        throw new Error(errorDetails)
      }

      const data = await response.json()

      console.log("Explore API response:", data)

      if (data.success) {
        if (data.data && Array.isArray(data.data)) {
          console.log("Received items:", data.data.length)

          if (append) {
            setResults((prev) => [...prev, ...data.data])
          } else {
            setResults(data.data)
          }

          // 使用API返回的hasMore欄位，或者根據返回的數據量判斷
          const hasMoreData = data.pagination?.hasMore ?? (data.data.length === limit)
          setHasMore(hasMoreData)

          console.log("Current pagination state:", {
            currentPage: pageNum,
            totalPages: data.pagination?.totalPages,
            returnedItems: data.data.length,
            limit: limit,
            hasMore: hasMoreData,
          })
        } else {
          console.error("Invalid data format:", data)
          setError("返回的數據格式無效")
          if (!append) setResults([])
        }
      } else {
        console.error("API returned error:", data.error)
        setError(data.error || "獲取數據失敗")
        if (!append) setResults([])
      }
    } catch (err) {
      console.error("Error fetching explore data:", err)
      setError(err instanceof Error ? err.message : "獲取數據失敗")
      if (!append) setResults([])
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }

  // 加載更多數據
  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      const nextPage = page + 1
      setPage(nextPage)
      fetchData(nextPage, true)
    }
  }, [page, isLoadingMore, hasMore, type, sort, searchQuery, selectedSemanticTypes, selectedTopics, selectedSubtopics])

  // 無限滾動監聽
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop >= document.documentElement.offsetHeight - 500) {
        loadMore()
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [loadMore])

  // 當篩選條件變化時重新獲取數據
  useEffect(() => {
    setPage(1)
    setHasMore(true)
    fetchData(1, false)
  }, [type, sort, searchQuery, selectedSemanticTypes, selectedTopics, selectedSubtopics])

  // 處理搜尋
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setSearchQuery(searchInput)
  }

  // 清除搜尋
  const clearSearch = () => {
    setSearchInput("")
    setSearchQuery("")
  }

  // 添加語義類型過濾器
  const toggleSemanticType = (semanticType: string) => {
    setSelectedSemanticTypes((prev) =>
      prev.includes(semanticType)
        ? prev.filter((t) => t !== semanticType)
        : [...prev, semanticType]
    )
  }

  // 添加主題過濾器
  const toggleTopic = (topic: string) => {
    setSelectedTopics((prev) =>
      prev.includes(topic)
        ? prev.filter((t) => t !== topic)
        : [...prev, topic]
    )
  }

  // 添加子主題過濾器
  const toggleSubtopic = (subtopic: string) => {
    setSelectedSubtopics((prev) =>
      prev.includes(subtopic)
        ? prev.filter((t) => t !== subtopic)
        : [...prev, subtopic]
    )
  }

  // 清除所有過濾器
  const clearFilters = () => {
    setSelectedSemanticTypes([])
    setSelectedTopics([])
    setSelectedSubtopics([])
  }

  // 獲取語義類型標籤
  const getSemanticTypeLabel = (value: string) => {
    return SEMANTIC_TYPES.find(type => type.value === value)?.label || value
  }

  // 獲取主題標籤
  const getTopicLabel = (value: string) => {
    const topic = availableTopics.find(topic => topic.name === value || topic.slug === value)
    return topic?.name || value
  }

  // 獲取子主題標籤
  const getSubtopicLabel = (value: string) => {
    const subtopic = availableSubtopics.find(subtopic => subtopic.name === value || subtopic.slug === value)
    return subtopic?.name || value
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">探索</h1>
          <div className="flex items-center gap-2">
            <form onSubmit={handleSearch} className="relative">
              <Input
                type="search"
                placeholder="搜尋..."
                className="w-[200px] md:w-[300px]"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              />
              {searchInput && (
                <button
                  type="button"
                  onClick={clearSearch}
                  className="absolute right-8 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <Search className="h-4 w-4" />
              </button>
            </form>
          </div>
        </div>

        <Tabs defaultValue={type} onValueChange={(value) => setType(value)}>
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="all">全部內容</TabsTrigger>
              <TabsTrigger value="viewpoint">觀點卡</TabsTrigger>
              <TabsTrigger value="discussion">討論</TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              {/* 過濾器按鈕 */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9">
                    <Filter className="h-4 w-4 mr-2" />
                    過濾器
                    {(selectedSemanticTypes.length > 0 || selectedTopics.length > 0 || selectedSubtopics.length > 0) && (
                      <Badge variant="destructive" className="ml-2 h-4 w-4 p-0 text-xs flex items-center justify-center">
                        {selectedSemanticTypes.length + selectedTopics.length + selectedSubtopics.length}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80" align="end">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">過濾條件</h4>
                      {(selectedSemanticTypes.length > 0 || selectedTopics.length > 0 || selectedSubtopics.length > 0) && (
                        <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2 text-xs">
                          清除全部
                        </Button>
                      )}
                    </div>

                    <div>
                      <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">內容類型</h5>
                      <div className="grid grid-cols-2 gap-2">
                        {SEMANTIC_TYPES.map((semanticType) => (
                          <div key={semanticType.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={semanticType.value}
                              checked={selectedSemanticTypes.includes(semanticType.value)}
                              onCheckedChange={() => toggleSemanticType(semanticType.value)}
                              className="h-4 w-4"
                            />
                            <label htmlFor={semanticType.value} className="text-sm cursor-pointer">
                              {semanticType.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">主題領域</h5>
                      {isLoadingFilters ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="ml-2 text-sm text-muted-foreground">載入中...</span>
                        </div>
                      ) : (
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {availableTopics.map((topic) => (
                            <div key={topic.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`topic-${topic.id}`}
                                checked={selectedTopics.includes(topic.name)}
                                onCheckedChange={() => toggleTopic(topic.name)}
                                className="h-4 w-4"
                              />
                              <label htmlFor={`topic-${topic.id}`} className="text-sm cursor-pointer font-medium">
                                {topic.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <Separator />

                    <div>
                      <h5 className="text-xs font-medium text-muted-foreground mb-3 uppercase tracking-wide">子主題</h5>
                      {isLoadingFilters ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="ml-2 text-sm text-muted-foreground">載入中...</span>
                        </div>
                      ) : (
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {availableSubtopics.map((subtopic) => (
                            <div key={subtopic.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`subtopic-${subtopic.id}`}
                                checked={selectedSubtopics.includes(subtopic.name)}
                                onCheckedChange={() => toggleSubtopic(subtopic.name)}
                                className="h-4 w-4"
                              />
                              <label htmlFor={`subtopic-${subtopic.id}`} className="text-sm cursor-pointer">
                                {subtopic.name}
                                {subtopic.topics && (
                                  <span className="text-xs text-muted-foreground ml-1">
                                    ({subtopic.topics.name})
                                  </span>
                                )}
                              </label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <Select defaultValue={sort} onValueChange={(value) => setSort(value)}>
                <SelectTrigger className="w-[140px] h-9">
                  <SelectValue placeholder="排序" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="trending">熱門</SelectItem>
                  <SelectItem value="latest">最新</SelectItem>
                  <SelectItem value="most_liked">最多按讚</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator className="my-4" />

          {/* 顯示已選擇的過濾器 */}
          {(selectedSemanticTypes.length > 0 || selectedTopics.length > 0 || selectedSubtopics.length > 0) && (
            <div className="flex flex-wrap gap-2 mb-4">
              {selectedSemanticTypes.map((type) => (
                <Badge
                  key={type}
                  variant="secondary"
                  className="cursor-pointer hover:bg-secondary/80"
                  onClick={() => toggleSemanticType(type)}
                >
                  {getSemanticTypeLabel(type)} <X className="ml-1 h-3 w-3" />
                </Badge>
              ))}
              {selectedTopics.map((topic) => (
                <Badge
                  key={topic}
                  variant="outline"
                  className="cursor-pointer hover:bg-accent"
                  onClick={() => toggleTopic(topic)}
                >
                  {getTopicLabel(topic)} <X className="ml-1 h-3 w-3" />
                </Badge>
              ))}
              {selectedSubtopics.map((subtopic) => (
                <Badge
                  key={subtopic}
                  variant="outline"
                  className="cursor-pointer hover:bg-accent border-dashed"
                  onClick={() => toggleSubtopic(subtopic)}
                >
                  #{getSubtopicLabel(subtopic)} <X className="ml-1 h-3 w-3" />
                </Badge>
              ))}
              <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2 text-xs">
                清除全部
              </Button>
            </div>
          )}

          {/* 顯示錯誤訊息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              <p className="font-medium">載入錯誤</p>
              <p className="text-sm">{error}</p>
              <Button variant="outline" size="sm" className="mt-2" onClick={() => fetchData(1, false)}>
                重試
              </Button>
            </div>
          )}

          {/* 初始載入狀態 */}
          {isLoading && results.length === 0 && (
            <div className="flex justify-center my-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}

          {/* 內容區域 - 使用網格佈局 */}
          <TabsContent value="all" className="mt-6">
            {!isLoading && results.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-lg text-muted-foreground">未找到內容</p>
                <p className="text-sm text-muted-foreground mt-2">嘗試調整過濾器或搜尋條件</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {results.map((item) => (
                    <ContentCard key={`${item.contentType}-${item.id}`} {...item} variant="grid" />
                  ))}
                </div>

                {/* 載入更多指示器 */}
                {isLoadingMore && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>載入更多內容...</span>
                    </div>
                  </div>
                )}

                {/* 沒有更多內容提示 */}
                {!hasMore && results.length > 0 && (
                  <div className="text-center mt-8 text-muted-foreground">
                    <p>已載入全部內容</p>
                  </div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="viewpoint" className="mt-6">
            {!isLoading && results.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-lg text-muted-foreground">未找到觀點卡</p>
                <p className="text-sm text-muted-foreground mt-2">嘗試調整過濾器或搜尋條件</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {results.map((item) => (
                    <ContentCard key={`viewpoint-${item.id}`} {...item} variant="grid" />
                  ))}
                </div>

                {isLoadingMore && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>載入更多內容...</span>
                    </div>
                  </div>
                )}

                {!hasMore && results.length > 0 && (
                  <div className="text-center mt-8 text-muted-foreground">
                    <p>已載入全部內容</p>
                  </div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="discussion" className="mt-6">
            {!isLoading && results.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-lg text-muted-foreground">未找到討論</p>
                <p className="text-sm text-muted-foreground mt-2">嘗試調整過濾器或搜尋條件</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {results.map((item) => (
                    <ContentCard key={`discussion-${item.id}`} {...item} variant="grid" />
                  ))}
                </div>

                {isLoadingMore && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>載入更多內容...</span>
                    </div>
                  </div>
                )}

                {!hasMore && results.length > 0 && (
                  <div className="text-center mt-8 text-muted-foreground">
                    <p>已載入全部內容</p>
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
