"use client"

import { useState } from "react"
import { ContentCard } from "@/components/content-card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"

// 示例觀點卡數據
const sampleViewpoints = [
  {
    id: 1,
    semanticType: "insight" as const,
    title: "GPT-4o 與 Claude 3 Opus 的實際應用比較",
    content:
      "經過多次實測，我發現 GPT-4o 在創意寫作和多模態理解上表現更佳，而 Claude 3 Opus 在長文本分析和事實準確性方面略勝一籌。對於需要處理大量文本數據的企業應用，Claude 可能是更好的選擇；而對於需要創意內容生成的場景，GPT-4o 則更為適合。\n\n值得注意的是，GPT-4o 的響應速度明顯快於 Claude 3 Opus，這在實時應用場景中是一個重要優勢。",
    topics: ["LLM"],
    subtopics: ["GPT-4o", "Claude 3", "模型比較"],
    author: {
      id: "user1",
      name: "AI研究員",
      avatar: "/diverse-research-team.png",
    },
    sourceType: "top_author" as const,
    timestamp: "2小時前",
    stats: {
      likes: 42,
      dislikes: 3,
      comments: 12,
      bookmarks: 18,
    },
  },
  {
    id: 2,
    semanticType: "experience" as const,
    title: "使用 LoRA 微調 Llama 3 的實戰經驗分享",
    content:
      "最近嘗試使用 LoRA 技術微調 Llama 3 模型，發現幾個關鍵優化點。首先，參數設置對性能影響顯著，尤其是 rank 和 alpha 值的選擇。其次，訓練數據的質量比數量更重要，精心準備的少量高質量數據往往能帶來更好的效果。\n\n實踐建議：\n- 從小型數據集開始，逐步擴大\n- 使用 rank=16, alpha=32 作為起點\n- 監控驗證損失避免過擬合",
    topics: ["Fine-tuning"],
    subtopics: ["LoRA", "Llama 3", "參數優化"],
    author: {
      id: "user2",
      name: "技術愛好者",
      avatar: "/mystical-forest-spirit.png",
    },
    sourceType: "community" as const,
    timestamp: "1天前",
    stats: {
      likes: 28,
      dislikes: 2,
      comments: 8,
      bookmarks: 15,
    },
  },
  {
    id: 3,
    semanticType: "guide" as const,
    title: "使用 LangChain 構建高效 RAG 系統的完整指南",
    content:
      "本指南將帶你一步步構建一個高效的 RAG 系統。首先，我們需要選擇合適的向量數據庫，如 FAISS 或 Pinecone。然後，設計合理的文檔分塊策略，確保語義完整性。接著，實現檢索策略，包括混合檢索和重排序機制。最後，優化提示模板，確保生成的回答準確且連貫。\n\n完整代碼示例和配置參數請參考原文。",
    topics: ["RAG"],
    subtopics: ["LangChain", "向量數據庫", "檢索策略"],
    author: {
      id: "user3",
      name: "系統架構師",
      avatar: "/modern-architect-studio.png",
    },
    sourceType: "top_author" as const,
    timestamp: "3天前",
    stats: {
      likes: 56,
      dislikes: 1,
      comments: 14,
      bookmarks: 32,
    },
  },
]

// 示例討論數據
const sampleDiscussions = [
  {
    id: 1,
    semanticType: "discussion" as const,
    title: "人工智能在醫療領域的應用前景如何？",
    content:
      "隨著人工智能技術的快速發展，它在醫療領域的應用也越來越廣泛。從輔助診斷到藥物研發，AI似乎正在改變醫療行業的方方面面。但是，AI在醫療領域的應用也面臨著數據隱私、算法透明度等挑戰。大家對此有什麼看法？",
    author: {
      id: "user1",
      name: "醫學研究者",
      avatar: "/diverse-research-team.png",
    },
    timestamp: "2小時前",
    tags: ["人工智能", "醫療科技", "倫理討論"],
    topics: ["AI應用"],
    stats: {
      replies: 24,
      views: 156,
      likes: 42,
    },
    isHot: true,
  },
  {
    id: 2,
    semanticType: "question" as const,
    title: "如何有效提高英語口語水平？",
    content:
      "我已經學習英語多年，但口語表達仍然是我的弱項。我嘗試過看英語電影、聽播客等方法，但進步不明顯。有沒有什麼更有效的方法可以提高英語口語水平？特別是對於已經有一定基礎但需要突破的學習者。",
    author: {
      id: "user2",
      name: "語言學習者",
      avatar: "/mystical-forest-spirit.png",
    },
    timestamp: "昨天",
    tags: ["英語學習", "語言技巧", "自學方法"],
    topics: ["語言學習"],
    stats: {
      replies: 18,
      views: 89,
      likes: 15,
    },
    isNew: true,
  },
  {
    id: 3,
    semanticType: "brainstorm" as const,
    title: "遠程工作的未來趨勢",
    content:
      "疫情加速了遠程工作的普及，許多公司發現遠程工作模式不僅可行，而且在某些方面還提高了效率。隨著技術的發展和工作觀念的改變，遠程工作是否會成為未來的主流工作方式？它將如何改變我們的職業生涯和生活方式？",
    author: {
      id: "user3",
      name: "職場觀察家",
      avatar: "/modern-architect-studio.png",
    },
    timestamp: "3天前",
    tags: ["遠程工作", "職場趨勢", "數字游民"],
    topics: ["工作趨勢"],
    stats: {
      replies: 32,
      views: 215,
      likes: 67,
    },
    status: "draft" as const,
  },
]

export default function ContentCardDemo() {
  const [contentType, setContentType] = useState<"viewpoint" | "discussion">("viewpoint")
  const [variant, setVariant] = useState<"card" | "compact" | "grid" | "quote">("card")
  const [isCompact, setIsCompact] = useState(false)
  const [columns, setColumns] = useState<1 | 2 | 3>(2)

  // 處理卡片操作
  const handleAction = (action: string, id: number) => {
    console.log(`Action: ${action}, ID: ${id}`)
  }

  // 根據內容類型獲取示例數據
  const sampleData = contentType === "viewpoint" ? sampleViewpoints : sampleDiscussions

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">統一內容卡片展示</h1>
        <p className="text-muted-foreground">展示新的統一內容卡片設計，支持觀點卡和討論串的所有變體和功能</p>
      </div>

      {/* 控制面板 */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="space-x-2">
          <Button
            variant={contentType === "viewpoint" ? "default" : "outline"}
            onClick={() => setContentType("viewpoint")}
          >
            觀點卡
          </Button>
          <Button
            variant={contentType === "discussion" ? "default" : "outline"}
            onClick={() => setContentType("discussion")}
          >
            討論串
          </Button>
        </div>

        <Separator orientation="vertical" className="h-8" />

        <div className="space-x-2">
          <Button variant={variant === "card" ? "default" : "outline"} onClick={() => setVariant("card")} size="sm">
            卡片式
          </Button>
          <Button
            variant={variant === "compact" ? "default" : "outline"}
            onClick={() => setVariant("compact")}
            size="sm"
          >
            精簡式
          </Button>
          <Button variant={variant === "grid" ? "default" : "outline"} onClick={() => setVariant("grid")} size="sm">
            網格式
          </Button>
          <Button variant={variant === "quote" ? "default" : "outline"} onClick={() => setVariant("quote")} size="sm">
            引用式
          </Button>
        </div>

        {variant === "grid" && (
          <>
            <Separator orientation="vertical" className="h-8" />
            <div className="space-x-2">
              <Button variant={columns === 1 ? "default" : "outline"} onClick={() => setColumns(1)} size="sm">
                1列
              </Button>
              <Button variant={columns === 2 ? "default" : "outline"} onClick={() => setColumns(2)} size="sm">
                2列
              </Button>
              <Button variant={columns === 3 ? "default" : "outline"} onClick={() => setColumns(3)} size="sm">
                3列
              </Button>
            </div>
          </>
        )}

        <Separator orientation="vertical" className="h-8" />
        <div className="space-x-2">
          <Button variant={!isCompact ? "default" : "outline"} onClick={() => setIsCompact(false)} size="sm">
            完整內容
          </Button>
          <Button variant={isCompact ? "default" : "outline"} onClick={() => setIsCompact(true)} size="sm">
            精簡模式
          </Button>
        </div>
      </div>

      {/* 內容展示區 */}
      <div className="mt-8">
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">全部</TabsTrigger>
            {contentType === "viewpoint" ? (
              <>
                <TabsTrigger value="insight">看法</TabsTrigger>
                <TabsTrigger value="experience">實測經驗</TabsTrigger>
                <TabsTrigger value="guide">工具教學</TabsTrigger>
              </>
            ) : (
              <>
                <TabsTrigger value="discussion">討論</TabsTrigger>
                <TabsTrigger value="question">問題</TabsTrigger>
                <TabsTrigger value="brainstorm">集思廣益</TabsTrigger>
              </>
            )}
          </TabsList>

          <TabsContent value="all" className="mt-0">
            {variant === "grid" ? (
              <div
                className={`grid gap-4 ${
                  columns === 1
                    ? "grid-cols-1"
                    : columns === 2
                      ? "grid-cols-1 md:grid-cols-2"
                      : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                }`}
              >
                {sampleData.map((item) =>
                  contentType === "viewpoint" ? (
                    <ContentCard
                      key={item.id}
                      contentType="viewpoint"
                      id={item.id}
                      semanticType={item.semanticType as any}
                      title={item.title}
                      content={item.content}
                      author={item.author}
                      topics={item.topics}
                      subtopics={item.subtopics}
                      sourceType={(item as any).sourceType}
                      timestamp={item.timestamp}
                      stats={item.stats}
                      variant={variant}
                      isCompact={isCompact}
                      onAction={handleAction}
                    />
                  ) : (
                    <ContentCard
                      key={item.id}
                      contentType="discussion"
                      id={item.id}
                      semanticType={item.semanticType as any}
                      title={item.title}
                      content={item.content}
                      author={item.author}
                      topics={item.topics}
                      tags={item.tags}
                      timestamp={item.timestamp}
                      stats={item.stats}
                      isHot={(item as any).isHot}
                      isNew={(item as any).isNew}
                      status={(item as any).status}
                      variant={variant}
                      isCompact={isCompact}
                      onAction={handleAction}
                    />
                  ),
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {sampleData.map((item) =>
                  contentType === "viewpoint" ? (
                    <ContentCard
                      key={item.id}
                      contentType="viewpoint"
                      id={item.id}
                      semanticType={item.semanticType as any}
                      title={item.title}
                      content={item.content}
                      author={item.author}
                      topics={item.topics}
                      subtopics={item.subtopics}
                      sourceType={(item as any).sourceType}
                      timestamp={item.timestamp}
                      stats={item.stats}
                      variant={variant}
                      isCompact={isCompact}
                      onAction={handleAction}
                    />
                  ) : (
                    <ContentCard
                      key={item.id}
                      contentType="discussion"
                      id={item.id}
                      semanticType={item.semanticType as any}
                      title={item.title}
                      content={item.content}
                      author={item.author}
                      topics={item.topics}
                      tags={item.tags}
                      timestamp={item.timestamp}
                      stats={item.stats}
                      isHot={(item as any).isHot}
                      isNew={(item as any).isNew}
                      status={(item as any).status}
                      variant={variant}
                      isCompact={isCompact}
                      onAction={handleAction}
                    />
                  ),
                )}
              </div>
            )}
          </TabsContent>

          {/* 觀點卡語義類型標籤頁 */}
          {contentType === "viewpoint" && (
            <>
              <TabsContent value="insight" className="mt-0">
                <div className="space-y-4">
                  {sampleViewpoints
                    .filter((item) => item.semanticType === "insight")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="viewpoint"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        subtopics={item.subtopics}
                        sourceType={item.sourceType}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="experience" className="mt-0">
                <div className="space-y-4">
                  {sampleViewpoints
                    .filter((item) => item.semanticType === "experience")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="viewpoint"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        subtopics={item.subtopics}
                        sourceType={item.sourceType}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="guide" className="mt-0">
                <div className="space-y-4">
                  {sampleViewpoints
                    .filter((item) => item.semanticType === "guide")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="viewpoint"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        subtopics={item.subtopics}
                        sourceType={item.sourceType}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>
            </>
          )}

          {/* 討論語義類型標籤頁 */}
          {contentType === "discussion" && (
            <>
              <TabsContent value="discussion" className="mt-0">
                <div className="space-y-4">
                  {sampleDiscussions
                    .filter((item) => item.semanticType === "discussion")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="discussion"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        tags={item.tags}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        isHot={item.isHot}
                        isNew={item.isNew}
                        status={item.status}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="question" className="mt-0">
                <div className="space-y-4">
                  {sampleDiscussions
                    .filter((item) => item.semanticType === "question")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="discussion"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        tags={item.tags}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        isHot={item.isHot}
                        isNew={item.isNew}
                        status={item.status}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="brainstorm" className="mt-0">
                <div className="space-y-4">
                  {sampleDiscussions
                    .filter((item) => item.semanticType === "brainstorm")
                    .map((item) => (
                      <ContentCard
                        key={item.id}
                        contentType="discussion"
                        id={item.id}
                        semanticType={item.semanticType}
                        title={item.title}
                        content={item.content}
                        author={item.author}
                        topics={item.topics}
                        tags={item.tags}
                        timestamp={item.timestamp}
                        stats={item.stats}
                        isHot={item.isHot}
                        isNew={item.isNew}
                        status={item.status}
                        variant={variant}
                        isCompact={isCompact}
                        onAction={handleAction}
                      />
                    ))}
                </div>
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  )
}
