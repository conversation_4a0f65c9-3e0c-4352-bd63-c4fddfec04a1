import { getAllTopics } from "@/lib/topic-service"
import Link from "next/link"

export default async function TopicsPage() {
  const response = await getAllTopics()
  const topics = response.success ? response.data || [] : []

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">所有主題</h1>

      {topics.length === 0 ? (
        <p>沒有找到任何主題。</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {topics.map((topic) => (
            <div key={topic.id} className="border rounded-lg p-4 shadow-sm">
              <h2 className="text-xl font-semibold mb-2">{topic.name}</h2>
              <p className="text-gray-600 mb-4">{topic.description}</p>
              <div className="flex justify-between items-center">
                <Link href={`/topic/${encodeURIComponent(topic.name)}`} className="text-blue-600 hover:underline">
                  查看主題
                </Link>
                <span className="text-sm text-gray-500">{topic.subtopics?.length || 0} 個子主題</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
