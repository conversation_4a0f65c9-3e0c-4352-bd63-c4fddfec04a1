"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { LibrarySidebar } from "@/components/library-sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import {
  Pencil,
  Share2,
  Trash2,
  MoreHorizontal,
  Search,
  Loader2,
  BookmarkIcon,
  Plus,
  Filter,
  Check,
  ArrowLeft,
} from "lucide-react"
import { mockCollections, mockBookmarkedItems, mockCategories, mockTags } from "@/lib/mock-data"
import { ContentCard } from "@/components/content-card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function CollectionDetailPage({ params }: { params: { id: string } }) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false)
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [searchItemQuery, setSearchItemQuery] = useState("")
  const [activeCollection, setActiveCollection] = useState<string | undefined>(undefined)

  // 獲取收藏牆數據
  const collection = mockCollections.find((c) => c.id.toString() === params.id)

  // 如果未登入，重定向到登入頁面
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])

  // 設置當前活動的收藏牆
  useEffect(() => {
    if (collection) {
      setActiveCollection(collection.name)
    }
  }, [collection])

  // 如果收藏牆不存在，顯示錯誤
  if (!collection && !isLoading) {
    return (
      <div className="flex">
        <LibrarySidebar
          categories={mockCategories}
          tags={mockTags}
          activeCategory={undefined}
          activeCollection={activeCollection}
          activeTag={undefined}
          onCategoryClick={() => { }}
          onCollectionClick={(name) => setActiveCollection(name)}
          onTagClick={() => { }}
          onQuickLinkClick={() => { }}
          onCreateCollection={() => { }}
          onCreateCategory={() => { }}
          onAddToCollection={() => { }}
        />
        <div className="flex-1 p-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold">收藏牆不存在</h2>
            <p className="mt-2 text-muted-foreground">您請求的收藏牆不存在或已被刪除</p>
            <Button className="mt-4" onClick={() => router.push("/library")}>
              返回收藏庫
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // 獲取收藏牆中的項目
  const collectionItems = mockBookmarkedItems.filter((item) => {
    // 模擬項目屬於此收藏牆
    const belongsToCollection = item.id % 3 === Number.parseInt(params.id) % 3

    if (!belongsToCollection) return false

    // 根據標籤過濾
    if (activeTab !== "all" && item.type !== activeTab) {
      return false
    }

    // 根據搜索查詢過濾
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.tags?.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    return true
  })

  // 獲取可添加到收藏牆的項目
  const availableItems = mockBookmarkedItems.filter((item) => {
    // 模擬項目不屬於此收藏牆
    const notInCollection = item.id % 3 !== Number.parseInt(params.id) % 3

    // 根據搜索查詢過濾
    if (searchItemQuery) {
      const query = searchItemQuery.toLowerCase()
      return (
        notInCollection &&
        (item.title.toLowerCase().includes(query) ||
          item.content.toLowerCase().includes(query) ||
          item.tags?.some((tag) => tag.toLowerCase().includes(query)))
      )
    }

    return notInCollection
  })

  // 處理刪除收藏牆
  const handleDeleteCollection = () => {
    // 這裡會有實際的刪除收藏牆邏輯
    console.log("Deleting collection:", params.id)
    setIsDeleteDialogOpen(false)
    router.push("/library")
  }

  // 處理添加項目到收藏牆
  const handleAddItems = () => {
    // 這裡會有實際的添加項目邏輯
    console.log("Adding items to collection:", selectedItems)
    setIsAddItemDialogOpen(false)
    setSelectedItems([])
  }

  // 處理項目選擇
  const toggleItemSelection = (id: number) => {
    setSelectedItems((prev) => (prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]))
  }

  // 處理收藏牆分類和收藏牆點擊
  const handleCategoryClick = (category: string) => {
    // 處理分類點擊邏輯
    console.log("Category clicked:", category)
  }

  const handleCollectionClick = (collection: string) => {
    // 更新活動收藏牆
    setActiveCollection(collection)
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="flex">
      {/* 左側收藏牆側邊欄 */}
      <LibrarySidebar
        categories={mockCategories}
        tags={mockTags}
        activeCategory={undefined}
        activeCollection={activeCollection}
        activeTag={undefined}
        onCategoryClick={handleCategoryClick}
        onCollectionClick={handleCollectionClick}
        onTagClick={() => { }}
        onQuickLinkClick={() => { }}
        onCreateCollection={() => { }}
        onCreateCategory={() => { }}
        onAddToCollection={() => { }}
      />

      {/* 主要內容區域 */}
      <div className="flex-1 p-6">
        {collection && (
          <>
            <div className="flex items-center gap-2 mb-4">
              <Button variant="ghost" size="sm" onClick={() => router.push("/library")} className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回收藏庫
              </Button>
            </div>

            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-3xl font-bold">{collection.name}</h1>
                <p className="text-muted-foreground mt-1">{collection.description}</p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setIsAddItemDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加項目
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => router.push(`/library/collection/${params.id}/edit`)}>
                      <Pencil className="mr-2 h-4 w-4" />
                      編輯收藏牆
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="mr-2 h-4 w-4" />
                      分享收藏牆
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      刪除收藏牆
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* 搜索和過濾 */}
            <div className="mt-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="flex items-center justify-between border-b pb-2">
                  <TabsList>
                    <TabsTrigger value="all" className="flex items-center">
                      <BookmarkIcon className="mr-2 h-4 w-4" />
                      全部
                    </TabsTrigger>
                    <TabsTrigger value="viewpoint">觀點卡</TabsTrigger>
                    <TabsTrigger value="discussion">討論</TabsTrigger>
                  </TabsList>

                  <div className="flex items-center gap-2">
                    <div className="relative w-64">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="搜尋收藏項目..."
                        className="pl-8 h-9"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>

                    <Button variant="outline" size="icon" className="h-9 w-9">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* 標籤和過濾器顯示 */}
                {searchQuery && (
                  <div className="flex items-center gap-2 my-4">
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      搜尋: {searchQuery}
                      <Button variant="ghost" size="icon" className="h-4 w-4 ml-1" onClick={() => setSearchQuery("")}>
                        ✕
                      </Button>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        setSearchQuery("")
                      }}
                    >
                      清除全部
                    </Button>
                  </div>
                )}

                <TabsContent value="all">
                  {collectionItems.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                      {collectionItems.map((item) => (
                        <div key={item.id} className="h-full">
                          <ContentCard
                            id={item.id}
                            contentType={item.type === "viewpoint" ? "viewpoint" : "discussion"}
                            semanticType={item.semanticType}
                            title={item.title}
                            content={item.content}
                            topics={item.topics || []}
                            subtopics={item.tags || []}
                            tags={item.tags}
                            author={item.author}
                            sourceType={item.sourceType || "community"}
                            timestamp={item.timestamp}
                            stats={item.stats}
                            variant="grid"
                            features={{ truncate: true }}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <BookmarkIcon className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h2 className="mt-4 text-xl font-medium">此收藏牆暫無項目</h2>
                      <p className="mt-2 text-muted-foreground">
                        {searchQuery ? "沒有符合搜尋條件的收藏項目" : "點擊「添加項目」按鈕開始添加內容"}
                      </p>
                      <Button className="mt-4" onClick={() => setIsAddItemDialogOpen(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        添加項目
                      </Button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="viewpoint">
                  {collectionItems.filter((item) => item.type === "viewpoint").length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                      {collectionItems
                        .filter((item) => item.type === "viewpoint")
                        .map((item) => (
                          <div key={item.id} className="h-full">
                            <ContentCard
                              id={item.id}
                              contentType="viewpoint"
                              semanticType={item.semanticType}
                              title={item.title}
                              content={item.content}
                              topics={item.topics || []}
                              subtopics={item.tags || []}
                              author={item.author}
                              sourceType={item.sourceType || "community"}
                              timestamp={item.timestamp}
                              stats={item.stats}
                              variant="grid"
                              features={{ truncate: true }}
                            />
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <BookmarkIcon className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h2 className="mt-4 text-xl font-medium">此收藏牆暫無觀點卡</h2>
                      <p className="mt-2 text-muted-foreground">
                        {searchQuery ? "沒有符合搜尋條件的觀點卡" : "點擊「添加項目」按鈕開始添加觀點卡"}
                      </p>
                      <Button className="mt-4" onClick={() => setIsAddItemDialogOpen(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        添加項目
                      </Button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="discussion">
                  {collectionItems.filter((item) => item.type === "discussion").length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                      {collectionItems
                        .filter((item) => item.type === "discussion")
                        .map((item) => (
                          <div key={item.id} className="h-full">
                            <ContentCard
                              id={item.id}
                              contentType="discussion"
                              title={item.title}
                              content={item.content}
                              author={item.author}
                              timestamp={item.timestamp}
                              tags={item.tags}
                              semanticType={item.semanticType}
                              stats={item.stats}
                              variant="grid"
                              features={{ truncate: true }}
                            />
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <BookmarkIcon className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h2 className="mt-4 text-xl font-medium">此收藏牆暫無討論</h2>
                      <p className="mt-2 text-muted-foreground">
                        {searchQuery ? "沒有符合搜尋條件的討論" : "點擊「添加項目」按鈕開始添加討論"}
                      </p>
                      <Button className="mt-4" onClick={() => setIsAddItemDialogOpen(true)}>
                        <Plus className="mr-2 h-4 w-4" />
                        添加項目
                      </Button>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </>
        )}

        {/* 刪除收藏牆確認對話框 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>確認刪除</DialogTitle>
              <DialogDescription>
                您確定要刪除「{collection?.name}」收藏牆嗎？此操作無法撤銷，收藏牆中的所有項目將被移除。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button variant="destructive" onClick={handleDeleteCollection}>
                刪除
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 添加項目對話框 */}
        <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加項目到收藏牆</DialogTitle>
              <DialogDescription>選擇您想添加到「{collection?.name}」的項目。</DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {/* 搜索和過濾 */}
              <div className="flex items-center gap-2 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜尋項目..."
                    className="pl-8"
                    value={searchItemQuery}
                    onChange={(e) => setSearchItemQuery(e.target.value)}
                  />
                </div>
                <Button variant="outline" size="icon" className="h-9 w-9">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>

              {/* 項目列表 */}
              <ScrollArea className="h-[300px] pr-4">
                {availableItems.length > 0 ? (
                  <div className="space-y-2">
                    {availableItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-start gap-3 p-2 border rounded-md hover:bg-muted/50 cursor-pointer"
                        onClick={() => toggleItemSelection(item.id)}
                      >
                        <Checkbox checked={selectedItems.includes(item.id)} />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="outline"
                              className={
                                item.type === "viewpoint"
                                  ? "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300"
                                  : "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300"
                              }
                            >
                              {item.type === "viewpoint" ? "觀點卡" : "討論"}
                            </Badge>
                            <h4 className="font-medium text-sm">{item.title}</h4>
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-1 mt-1">{item.content}</p>
                          {item.tags && item.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {item.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs py-0 px-1.5 bg-secondary/40">
                                  {tag}
                                </Badge>
                              ))}
                              {item.tags.length > 3 && (
                                <Badge variant="secondary" className="text-xs py-0 px-1.5 bg-secondary/40">
                                  +{item.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">沒有可添加的項目</p>
                  </div>
                )}
              </ScrollArea>
            </div>

            <DialogFooter>
              <div className="flex items-center mr-auto text-sm">已選擇 {selectedItems.length} 個項目</div>
              <Button variant="outline" onClick={() => setIsAddItemDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddItems} disabled={selectedItems.length === 0}>
                <Check className="mr-2 h-4 w-4" />
                添加
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
