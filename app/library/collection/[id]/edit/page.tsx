"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { useAuth } from "@/contexts/auth-context"
import { getCollectionDetails, updateCollection, deleteCollection } from "@/lib/bookmark-service"
import { ChevronLeft, Loader2 } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function CollectionEditPage({ params }: { params: { id: string } }) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const [collection, setCollection] = useState<any>(null)
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [isPublic, setIsPublic] = useState(true)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // 如果未登入，重定向到登入頁面
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])

  // 獲取收藏牆詳情
  useEffect(() => {
    if (isAuthenticated && params.id) {
      const fetchData = async () => {
        setIsLoadingData(true)
        try {
          // 獲取收藏牆詳情
          const collectionResult = await getCollectionDetails(params.id)
          if (collectionResult.success && collectionResult.data) {
            const collectionData = collectionResult.data
            setCollection(collectionData)
            setName(collectionData.name)
            setDescription(collectionData.description || "")
            setIsPublic(collectionData.is_public)
          } else {
            // 如果獲取失敗，可能是權限問題或收藏牆不存在
            toast({
              title: "無法獲取收藏牆",
              description: "該收藏牆可能不存在或您沒有權限訪問",
              variant: "destructive",
            })
            router.push("/library")
            return
          }
        } catch (error) {
          console.error("Error fetching data:", error)
          toast({
            title: "獲取數據失敗",
            description: "請稍後再試",
            variant: "destructive",
          })
        } finally {
          setIsLoadingData(false)
        }
      }
      fetchData()
    }
  }, [isAuthenticated, params.id, router, toast])

  // 保存收藏牆
  const handleSave = async () => {
    if (!name.trim()) {
      toast({
        title: "請輸入收藏牆名稱",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)
    try {
      const { success } = await updateCollection(params.id, {
        name,
        description,
        isPublic,
      })
      if (success) {
        toast({
          title: "保存成功",
          description: "收藏牆已更新",
        })
        router.push(`/library/collection/${params.id}`)
      } else {
        toast({
          title: "保存失敗",
          description: "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error saving collection:", error)
      toast({
        title: "保存失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 刪除收藏牆
  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const { success } = await deleteCollection(params.id)
      if (success) {
        toast({
          title: "刪除成功",
          description: "收藏牆已刪除",
        })
        router.push("/library")
      } else {
        toast({
          title: "刪除失敗",
          description: "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting collection:", error)
      toast({
        title: "刪除失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (isLoadingData) {
    return (
      <div className="container mx-auto py-6 space-y-8">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" asChild className="mr-4">
            <Link href="/library">
              <ChevronLeft className="h-4 w-4 mr-1" />
              返回
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">載入中...</h1>
        </div>
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center">
        <Button variant="ghost" size="sm" asChild className="mr-4">
          <Link href={`/library/collection/${params.id}`}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            返回
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">編輯收藏牆</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>收藏牆設置</CardTitle>
          <CardDescription>更新您的收藏牆信息和隱私設置。</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">收藏牆名稱</Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} placeholder="輸入收藏牆名稱" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">描述 (選填)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="簡短描述這個收藏牆"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch id="public" checked={isPublic} onCheckedChange={setIsPublic} />
            <Label htmlFor="public">公開收藏牆</Label>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">刪除收藏牆</Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>確定要刪除嗎？</AlertDialogTitle>
                <AlertDialogDescription>
                  此操作將永久刪除此收藏牆及其中的所有項目。此操作無法撤銷。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : "確定刪除"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            保存更改
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
