import { createBrowserClient } from "@/lib/supabase/client"
import type { Database } from "@/lib/database.types"

type Collection = Database["public"]["Tables"]["collections"]["Row"]
type BookmarkedItem = Database["public"]["Tables"]["bookmarked_items"]["Row"]

// 獲取用戶的所有收藏牆
export async function getUserCollections(supabase?: any): Promise<Collection[]> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .select(`
        *,
        bookmarked_items (count)
      `)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching user collections:", error)
      return []
    }

    // 處理收藏項目計數
    const formattedData = data.map((collection) => ({
      ...collection,
      itemCount: collection.bookmarked_items?.[0]?.count || 0,
      bookmarked_items: undefined,
    }))

    return formattedData
  } catch (error) {
    console.error("Error in getUserCollections:", error)
    return []
  }
}

// 獲取單個收藏牆
export async function getCollectionById(collectionId: string, supabase?: any): Promise<Collection | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .select(`
        *,
        profiles:user_id (
          id,
          name,
          avatar
        ),
        bookmarked_items (count)
      `)
      .eq("id", collectionId)
      .single()

    if (error) {
      console.error("Error fetching collection:", error)
      return null
    }

    // 處理收藏項目計數
    const formattedData = {
      ...data,
      itemCount: data.bookmarked_items?.[0]?.count || 0,
      bookmarked_items: undefined,
    }

    return formattedData
  } catch (error) {
    console.error("Error in getCollectionById:", error)
    return null
  }
}

// 創建收藏牆
export async function createCollection(
  name: string,
  description?: string,
  isPublic = true,
  supabase?: any,
): Promise<Collection | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .insert({
        name,
        description,
        is_public: isPublic,
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating collection:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in createCollection:", error)
    return null
  }
}

// 更新收藏牆
export async function updateCollection(
  id: string,
  updates: Partial<Collection>,
  supabase?: any,
): Promise<Collection | null> {
  try {
    const client = supabase || createBrowserClient()

    const { data, error } = await client
      .from("collections")
      .update({
        name: updates.name,
        description: updates.description,
        is_public: updates.is_public,
        cover_image: updates.cover_image,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Error updating collection:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in updateCollection:", error)
    return null
  }
}

// 刪除收藏牆
export async function deleteCollection(id: string, supabase?: any): Promise<boolean> {
  try {
    const client = supabase || createBrowserClient()

    // 先刪除收藏牆中的所有項目
    const { error: itemsError } = await client.from("bookmarked_items").delete().eq("collection_id", id)

    if (itemsError) {
      console.error("Error deleting collection items:", itemsError)
      return false
    }

    // 然後刪除收藏牆本身
    const { error } = await client.from("collections").delete().eq("id", id)

    if (error) {
      console.error("Error deleting collection:", error)
      return false
    }

    return true
  } catch (error) {
    console.error("Error in deleteCollection:", error)
    return false
  }
}

// 獲取收藏牆中的項目
export async function getCollectionItems(
  collectionId: string,
  page = 1,
  limit = 20,
  supabase?: any,
): Promise<BookmarkedItem[]> {
  try {
    const client = supabase || createBrowserClient()
    const offset = (page - 1) * limit

    const { data, error } = await client
      .from("bookmarked_items")
      .select(`
        *,
        cards:card_id (*),
        discussions:discussion_id (*)
      `)
      .eq("collection_id", collectionId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Error fetching collection items:", error)
      return []
    }

    // 處理項目數據
    const formattedData = data.map((item) => ({
      ...item,
      item: item.card_id ? item.cards : item.discussions,
      itemType: item.card_id ? "card" : "discussion",
      // 移除原始關聯數據
      cards: undefined,
      discussions: undefined,
    }))

    return formattedData
  } catch (error) {
    console.error("Error in getCollectionItems:", error)
    return []
  }
}

// 添加項目到收藏牆
export async function addItemToCollection(
  collectionId: string,
  itemType: "card" | "discussion",
  itemId: string,
  supabase?: any,
): Promise<BookmarkedItem | null> {
  try {
    const client = supabase || createBrowserClient()

    // 檢查項目是否已經在收藏牆中
    const { data: existingItem, error: checkError } = await client
      .from("bookmarked_items")
      .select("*")
      .eq("collection_id", collectionId)
      .eq(itemType === "card" ? "card_id" : "discussion_id", itemId)
      .maybeSingle()

    if (checkError) {
      console.error("Error checking existing item:", checkError)
      return null
    }

    // 如果已經收藏，則返回錯誤
    if (existingItem) {
      console.warn("Item already in collection")
      return null
    }

    // 添加項目到收藏牆
    const { data, error } = await client
      .from("bookmarked_items")
      .insert({
        collection_id: collectionId,
        [itemType === "card" ? "card_id" : "discussion_id"]: itemId,
      })
      .select()
      .single()

    if (error) {
      console.error("Error adding item to collection:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in addItemToCollection:", error)
    return null
  }
}

// 從收藏牆中移除項目
export async function removeItemFromCollection(collectionId: string, itemId: string, supabase?: any): Promise<boolean> {
  try {
    const client = supabase || createBrowserClient()

    const { error } = await client.from("bookmarked_items").delete().eq("id", itemId).eq("collection_id", collectionId)

    if (error) {
      console.error("Error removing item from collection:", error)
      return false
    }

    return true
  } catch (error) {
    console.error("Error in removeItemFromCollection:", error)
    return false
  }
}

// 別名函數
export const getCollectionDetails = getCollectionById
