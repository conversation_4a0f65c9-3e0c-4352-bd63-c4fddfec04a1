export type TopicWithSubtopics = {
  id: string
  name: string
  description: string
  subtopics: Subtopic[]
}

export type ProfileUpdatePayload = {
  name?: string
  bio?: string
  avatar?: string // 修改為 avatar 而不是 avatar_url
}

export type Profile = {
  id: string
  name: string
  email: string
  avatar?: string // 修改為 avatar 而不是 avatar_url
  bio?: string
  created_at: string
  updated_at?: string
}

export type Topic = {
  id: string
  name: string
  description: string
  slug: string
  created_at: string
}

export type Subtopic = {
  id: string
  name: string
  topic_id: string
  description: string
  created_at: string
}

// Card 相關類型定義
export type CardType = "external" | "internal"
export type SemanticType = "insight" | "experience" | "guide" | "trap" | "debate" | "concept"
export type ContributionType = "top_author" | "community" | "curated" | "original" | "others"

export interface CardStats {
  likes: number;
  dislikes: number;
  comments: number;
  bookmarks: number;
  views?: number;
}

export interface Comment {
  id: string;
  author_id: string;
  content: string;
  root_item_id: string;
  root_item_type: "card" | "thread";
  parent_comment_id?: string | null;
  created_at: string;
  updated_at?: string;

  // Enriched data
  author: Profile;
  likes?: number;
  dislikes?: number;
  replies?: Comment[]; // For nested comments
  quotedCard?: {
    id: number;
    title: string;
    content?: string;
    semantic_type: SemanticType;
    author_name?: string;
    tags?: string[];
    topics?: Pick<Topic, 'id' | 'name' | 'slug'>[];
    reference_type?: string;
  };
}

export interface Card {
  id: number
  title: string
  content: string
  author_id: string
  card_type: CardType
  semantic_type: SemanticType
  contribution_type: ContributionType
  original_author?: string
  original_url?: string
  created_at: string
  updated_at: string
  // 關聯數據
  author?: Profile
  topics?: Topic[]
  subtopics?: Subtopic[]
  stats?: CardStats
  replies?: Comment[]
}

export interface CardCreatePayload {
  title: string
  content: string
  card_type: CardType
  semantic_type: SemanticType
  contribution_type: ContributionType
  original_author?: string
  original_url?: string
  topic_ids?: string[]
  subtopic_ids?: string[]
}

export interface CardUpdatePayload {
  title?: string
  content?: string
  card_type?: CardType
  semantic_type?: SemanticType
  contribution_type?: ContributionType
  original_author?: string
  original_url?: string
  topic_ids?: string[]
  subtopic_ids?: string[]
}

// 討論相關類型定義
export type SemanticDiscussionType = "discussion" | "question" | "debate" | "announcement"

export interface Discussion {
  id: string
  title: string
  content: string
  author_id: string
  semantic_type: SemanticDiscussionType
  created_at: string
  updated_at: string
  // 關聯數據
  author?: Profile
  topics?: Topic[]
}

export interface DiscussionCreateInput {
  title: string
  content: string
  authorId: string
  semanticType: SemanticDiscussionType
  topicIds?: string[]
}

export interface DiscussionUpdateInput {
  title?: string
  content?: string
  semanticType?: SemanticDiscussionType
  topicIds?: string[]
}

export interface Reply {
  id: string
  thread_id: string
  author_id: string
  content: string
  created_at: string
  updated_at: string
  // 關聯數據
  author?: Profile
}

// 分頁參數
export interface PaginationParams {
  page?: number
  limit?: number
}

// 卡片創建和更新輸入
export interface CardCreateInput {
  title: string
  content: string
  authorId: string
  semanticType: SemanticType
  sourceType: ContributionType
  originalSource?: string
  topicIds?: string[]
  subtopicIds?: string[]
}

export interface CardUpdateInput {
  title?: string
  content?: string
  semanticType?: SemanticType
  sourceType?: ContributionType
  originalSource?: string
  topicIds?: string[]
  subtopicIds?: string[]
}
