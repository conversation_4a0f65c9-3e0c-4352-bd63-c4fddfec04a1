import { getSupabase, type ApiResponse, handleError, successResponse } from "./api-utils"
import type { Discussion, Thread } from "@/lib/types"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// 獲取主題相關的討論串
export async function getThreadsByTopic(topicId: string, limit = 10): Promise<ApiResponse<Discussion[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("thread_topics")
      .select(`
        thread_id,
        threads:thread_id (
          id,
          title,
          content,
          author_id,
          semantic_type,
          status,
          created_at,
          updated_at,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("topic_id", topicId)
      .eq("threads.status", "published")
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Discussion 類型
    const threads = data.map((item: any) => ({
      id: item.threads.id,
      title: item.threads.title,
      content: item.threads.content,
      author_id: item.threads.author_id,
      semantic_type: item.threads.semantic_type,
      created_at: item.threads.created_at,
      updated_at: item.threads.updated_at || item.threads.created_at,
      author: item.threads.profiles,
    }))

    return successResponse(threads)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的評論數
export async function getThreadCommentCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("comments")
      .select("*", { count: "exact" })
      .eq("root_item_id", threadId)
      .eq("root_item_type", "thread")

    if (error) throw error

    return successResponse(count || 0)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的瀏覽數（假設有一個 thread_views 表）
export async function getThreadViewCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    // 這裡假設有一個 thread_views 表來記錄瀏覽數
    // 如果沒有，可以返回一個隨機數或固定值作為示例
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("thread_views")
      .select("*", { count: "exact" })
      .eq("thread_id", threadId)

    if (error) {
      // 如果表不存在，返回一個隨機數作為示例
      return successResponse(Math.floor(Math.random() * 1000))
    }

    return successResponse(count || 0)
  } catch (error) {
    // 如果出錯，返回一個隨機數作為示例
    return successResponse(Math.floor(Math.random() * 1000))
  }
}

// 獲取單個討論串詳情
export async function getThreadById(id: string): Promise<Thread | null> {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 1. 獲取討論基本信息
    const { data: thread, error } = await supabase
      .from("threads")
      .select(`
        *,
        author:author_id(id,name,email,avatar,bio),
        topics:thread_topics(topic:topic_id(id,name,description)),
        subtopics:thread_subtopics(subtopic:subtopic_id(id,name,description,topic_id))
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching thread:", error)
      return null
    }

    if (!thread) {
      return null
    }

    // 2. 獲取討論的評論
    const { data: comments, error: commentsError } = await supabase
      .from("comments")
      .select(`
        *,
        author:author_id(id,name,avatar,bio)
      `)
      .eq("root_item_type", "thread")
      .eq("root_item_id", id)
      .order("created_at", { ascending: true })

    if (commentsError) {
      console.error("Error fetching comments:", commentsError)
    }

    // 3. 獲取評論中引用的卡片
    const commentIds = comments ? comments.map((comment) => comment.id) : []
    const referencedCards = {}

    if (commentIds.length > 0) {
      const { data: references, error: referencesError } = await supabase
        .from("content_references")
        .select(`
          source_id,
          target_id,
          target_type,
          reference_type
        `)
        .eq("source_type", "comment")
        .in("source_id", commentIds)
        .eq("target_type", "card")

      if (referencesError) {
        console.error("Error fetching content references:", referencesError)
      } else if (references && references.length > 0) {
        // 獲取所有引用的卡片 ID
        const cardIds = references.map((ref) => ref.target_id)

        // 獲取引用的卡片詳情
        const { data: referencedCardsData, error: cardsError } = await supabase
          .from("cards")
          .select(`
            id,
            title,
            content,
            semantic_type,
            author:author_id(id,name)
          `)
          .in("id", cardIds)

        if (cardsError) {
          console.error("Error fetching referenced cards:", cardsError)
        } else if (referencedCardsData) {
          // 創建卡片 ID 到卡片詳情的映射
          referencedCardsData.forEach((card) => {
            referencedCards[card.id] = {
              id: card.id,
              title: card.title,
              content: card.content,
              type: card.semantic_type,
              author: card.author?.name || "未知作者",
              tags: [],
            }
          })

          // 將引用的卡片添加到對應的評論中
          if (comments) {
            comments.forEach((comment) => {
              const reference = references.find((ref) => ref.source_id === comment.id)
              if (reference && referencedCards[reference.target_id]) {
                comment.referenced_card = referencedCards[reference.target_id]
              }
            })
          }
        }
      }
    }

    // 4. 獲取討論的點讚數和倒讚數
    const { data: likesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "thread")
      .eq("item_id", id)
      .eq("reaction_type", "like")

    const { data: dislikesData } = await supabase
      .from("reactions")
      .select("id", { count: "exact" })
      .eq("item_type", "thread")
      .eq("item_id", id)
      .eq("reaction_type", "dislike")

    // 處理數據格式
    const formattedThread: Thread = {
      ...thread,
      topics: thread.topics.map((t: any) => t.topic),
      subtopics: thread.subtopics.map((s: any) => s.subtopic),
      replies: comments || [],
      stats: {
        views: 0, // 暫時沒有實現瀏覽量統計
        likes: likesData?.length || 0,
        dislikes: dislikesData?.length || 0,
        comments: comments?.length || 0,
      },
    }

    return formattedThread
  } catch (error) {
    console.error("Error in getThreadById:", error)
    return null
  }
}
