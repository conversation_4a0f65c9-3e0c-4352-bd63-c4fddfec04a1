import { useState, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"

interface BookmarkState {
    isBookmarked: boolean
    isLoading: boolean
    count: number
}

interface UseBookmarksOptions {
    itemId: string
    itemType: "card" | "thread"
    initialIsBookmarked?: boolean
    initialCount?: number
}

interface UseBookmarksReturn extends BookmarkState {
    toggleBookmark: () => Promise<void>
    refreshBookmarkStatus: () => Promise<void>
    refreshBookmarkCount: () => Promise<void>
}

export function useBookmarks({
    itemId,
    itemType,
    initialIsBookmarked = false,
    initialCount = 0,
}: UseBookmarksOptions): UseBookmarksReturn {
    const { toast } = useToast()
    const [state, setState] = useState<BookmarkState>({
        isBookmarked: initialIsBookmarked,
        isLoading: false,
        count: initialCount,
    })

    // 切換收藏狀態
    const toggleBookmark = useCallback(async () => {
        setState(prev => ({ ...prev, isLoading: true }))

        try {
            const response = await fetch("/api/bookmarks", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    itemType,
                    itemId,
                }),
            })

            const data = await response.json()

            if (!response.ok || !data.success) {
                throw new Error(data.error || "操作失敗")
            }

            const wasAdded = data.data.action === "added"

            setState(prev => ({
                ...prev,
                isBookmarked: wasAdded,
                count: wasAdded ? prev.count + 1 : Math.max(0, prev.count - 1),
            }))

            toast({
                title: data.message,
                description: wasAdded ? "已加入收藏列表" : "已從收藏列表移除",
            })

        } catch (error) {
            console.error("切換收藏狀態錯誤:", error)
            toast({
                title: "操作失敗",
                description: error instanceof Error ? error.message : "無法執行收藏操作",
                variant: "destructive",
            })
        } finally {
            setState(prev => ({ ...prev, isLoading: false }))
        }
    }, [itemId, itemType, toast])

    // 刷新收藏狀態
    const refreshBookmarkStatus = useCallback(async () => {
        try {
            const response = await fetch(`/api/bookmarks/check?itemType=${itemType}&itemId=${itemId}`)
            const data = await response.json()

            if (response.ok && data.success) {
                setState(prev => ({
                    ...prev,
                    isBookmarked: data.data.isBookmarked,
                }))
            }
        } catch (error) {
            console.error("獲取收藏狀態錯誤:", error)
        }
    }, [itemId, itemType])

    // 刷新收藏數量
    const refreshBookmarkCount = useCallback(async () => {
        try {
            const response = await fetch(`/api/bookmarks/count?itemType=${itemType}&itemId=${itemId}`)
            const data = await response.json()

            if (response.ok && data.success) {
                setState(prev => ({
                    ...prev,
                    count: data.data.count,
                }))
            }
        } catch (error) {
            console.error("獲取收藏數量錯誤:", error)
        }
    }, [itemId, itemType])

    return {
        ...state,
        toggleBookmark,
        refreshBookmarkStatus,
        refreshBookmarkCount,
    }
} 